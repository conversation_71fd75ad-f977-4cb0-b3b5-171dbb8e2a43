{"applications": ["Escrow"], "changelogDate": "20220330112824", "dto": "mapstruct", "embedded": false, "entityTableName": "project", "fields": [{"fieldName": "projectId", "fieldType": "String"}, {"fieldName": "cif", "fieldType": "String"}, {"fieldName": "name", "fieldType": "String"}, {"fieldName": "nameLocal", "fieldType": "String"}, {"fieldName": "masterDeveloperName", "fieldType": "String"}, {"fieldName": "location", "fieldType": "String"}, {"fieldName": "projectNumberRera", "fieldType": "String"}, {"fieldName": "startDate", "fieldType": "ZonedDateTime"}, {"fieldName": "completionDate", "fieldType": "ZonedDateTime"}, {"fieldName": "percentComplete", "fieldType": "Integer", "fieldValidateRules": ["max"], "fieldValidateRulesMax": "100"}, {"fieldName": "constructionCost", "fieldType": "Double"}, {"fieldName": "accStatusDate", "fieldType": "ZonedDateTime"}, {"fieldName": "registrationDate", "fieldType": "ZonedDateTime"}, {"fieldName": "noOfUnits", "fieldType": "Integer"}, {"fieldName": "remarks", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "specialApproval", "fieldType": "String"}, {"fieldName": "projectManagedBy", "fieldType": "String"}, {"fieldName": "projectBackupUser", "fieldType": "String"}, {"fieldName": "retentionPercent", "fieldType": "String"}, {"fieldName": "additionalRetentionPercent", "fieldType": "String"}, {"fieldName": "totalRetentionPercent", "fieldType": "String"}, {"fieldName": "retentionEffectiveDate", "fieldType": "ZonedDateTime"}, {"fieldName": "managementExpenses", "fieldType": "String"}, {"fieldName": "marketingExpenses", "fieldType": "String"}, {"fieldName": "projectAccoutStatusDate", "fieldType": "ZonedDateTime"}, {"fieldName": "teamLeaderName", "fieldType": "String"}, {"fieldName": "relationshipManagerName", "fieldType": "String"}, {"fieldName": "asstRelshipManagerName", "fieldType": "String"}, {"fieldName": "realEstateBrokerExp", "fieldType": "Double"}, {"fieldName": "advertisementExp", "fieldType": "Double"}, {"fieldName": "landOwnerName", "fieldType": "String"}, {"fieldName": "accOpenEmailSent", "fieldType": "String"}, {"fieldName": "accCloseEmailSent", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Project", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "developer", "otherEntityRelationshipName": "project", "relationshipName": "developer", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "projectStatus", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "projectType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "projectAccountStatus", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "constructionCostCurrency", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "project", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "otherEntityRelationshipName": "projectOption", "ownerSide": true, "relationshipName": "bucketType", "relationshipType": "many-to-many"}, {"otherEntityName": "appOption", "otherEntityRelationshipName": "projectBlockOption", "ownerSide": true, "relationshipName": "blockPayementType", "relationshipType": "many-to-many"}, {"otherEntityName": "projectFee", "otherEntityRelationshipName": "project", "relationshipName": "projectFee", "relationshipType": "one-to-many"}, {"otherEntityName": "projectFeeHistory", "otherEntityRelationshipName": "project", "relationshipName": "projectFeeHistory", "relationshipType": "one-to-many"}, {"otherEntityName": "projectFinancial", "otherEntityRelationshipName": "project", "relationshipName": "projectFinancial", "relationshipType": "one-to-many"}, {"otherEntityName": "projectClosure", "otherEntityRelationshipName": "project", "relationshipName": "projectClosure", "relationshipType": "one-to-many"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "project", "relationshipName": "unit", "relationshipType": "one-to-many"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "project", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}, {"otherEntityName": "guarantee", "otherEntityRelationshipName": "project", "relationshipName": "guarantee", "relationshipType": "one-to-many"}, {"otherEntityName": "paymentPlanCompletion", "otherEntityRelationshipName": "project", "relationshipName": "paymentPlanCompletion", "relationshipType": "one-to-many"}, {"otherEntityName": "paymentPlanInstallment", "otherEntityRelationshipName": "project", "relationshipName": "paymentPlanInstallment", "relationshipType": "one-to-many"}, {"otherEntityName": "reconTransaction", "otherEntityRelationshipName": "project", "relationshipName": "reconTransaction", "relationshipType": "one-to-many"}, {"otherEntityName": "nonReconTransaction", "otherEntityRelationshipName": "project", "relationshipName": "nonReconTransaction", "relationshipType": "one-to-many"}, {"otherEntityName": "voucherPayment", "otherEntityRelationshipName": "project", "relationshipName": "voucherPayment", "relationshipType": "one-to-many"}, {"otherEntityName": "<PERSON><PERSON><PERSON><PERSON>", "otherEntityRelationshipName": "project", "relationshipName": "<PERSON><PERSON><PERSON><PERSON>", "relationshipType": "one-to-many"}, {"otherEntityName": "balanceConfirmation", "otherEntityRelationshipName": "project", "relationshipName": "balanceConfirmation", "relationshipType": "one-to-many"}, {"otherEntityName": "depositBulkUpload", "otherEntityRelationshipName": "project", "relationshipName": "depositBulkUpload", "relationshipType": "one-to-many"}, {"otherEntityName": "projectBeneficiary", "otherEntityRelationshipName": "project", "ownerSide": false, "relationshipName": "projectBeneficiary", "relationshipType": "many-to-many"}], "service": "serviceImpl"}