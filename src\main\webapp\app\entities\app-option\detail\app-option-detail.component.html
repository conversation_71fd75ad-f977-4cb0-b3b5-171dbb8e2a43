<div class="d-flex justify-content-center">
  <div class="col-8">
    <div *ngIf="appOption">
      <h2 data-cy="appOptionDetailsHeading"><span>App Option</span></h2>

      <hr />

      <ab-alert-error></ab-alert-error>

      <ab-alert></ab-alert>

      <dl class="row-md jh-entity-details">
        <dt><span>ID</span></dt>
        <dd>
          <span>{{ appOption.id }}</span>
        </dd>
        <dt><span>Option Key</span></dt>
        <dd>
          <span>{{ appOption.optionKey }}</span>
        </dd>
        <dt><span>Option Value</span></dt>
        <dd>
          <span>{{ appOption.optionValue }}</span>
        </dd>
        <dt><span>Label Id</span></dt>
        <dd>
          <span>{{ appOption.labelId }}</span>
        </dd>
        <dt><span>Description</span></dt>
        <dd>
          <span>{{ appOption.description }}</span>
        </dd>
        <dt><span>Payment Fee Frequency</span></dt>
        <dd>
          <span *ngFor="let paymentFeeFrequency of appOption.paymentFeeFrequencies; let last = last">
            <a [routerLink]="['/app-option', paymentFeeFrequency.id, 'view']">{{ paymentFeeFrequency.id }}</a
            >{{ last ? '' : ', ' }}
          </span>
        </dd>
      </dl>

      <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
        <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span>Back</span>
      </button>

      <button type="button" [routerLink]="['/app-option', appOption.id, 'edit']" class="btn btn-primary">
        <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span>Edit</span>
      </button>
    </div>
  </div>
</div>
