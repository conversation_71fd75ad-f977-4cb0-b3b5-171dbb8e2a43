{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "bank_account", "fields": [{"fieldName": "bankAccountNumber", "fieldType": "String"}, {"fieldName": "bankAccountBalance", "fieldType": "Double"}, {"fieldName": "ibanNo", "fieldType": "String"}, {"fieldName": "openedDate", "fieldType": "ZonedDateTime"}, {"fieldName": "accountTitle", "fieldType": "String"}, {"fieldName": "swiftcode", "fieldType": "String"}, {"fieldName": "routingCode", "fieldType": "String"}, {"fieldName": "schemeType", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "BankAccount", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "branch", "otherEntityRelationshipName": "bankAccount", "relationshipName": "branch", "relationshipType": "many-to-one"}, {"otherEntityName": "bank", "otherEntityRelationshipName": "bankAccount", "relationshipName": "bank", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "bankCurrency", "relationshipType": "many-to-one"}, {"otherEntityName": "masterBankAccountType", "otherEntityRelationshipName": "bankAccount", "relationshipName": "masterBankAccountType", "relationshipType": "many-to-one"}, {"otherEntityName": "childBankAccountType", "otherEntityRelationshipName": "bankAccount", "relationshipName": "childBankAccountType", "relationshipType": "many-to-one"}, {"otherEntityName": "developerBeneficiary", "otherEntityRelationshipName": "bankAccount", "relationshipName": "developerBeneficiary", "relationshipType": "many-to-one"}, {"otherEntityName": "projectBeneficiary", "otherEntityRelationshipName": "bankAccount", "relationshipName": "projectBeneficiary", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "bankAccount", "relationshipName": "unit", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "bankAccount", "relationshipName": "project", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "bankAccount", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "propertyBeneficiary", "otherEntityRelationshipName": "bankAccount", "relationshipName": "propertyBeneficiary", "relationshipType": "many-to-one"}, {"otherEntityName": "hoaUnit", "otherEntityRelationshipName": "bankAccount", "relationshipName": "hoaUnit", "relationshipType": "many-to-one"}, {"otherEntityName": "property", "otherEntityRelationshipName": "bankAccount", "relationshipName": "property", "relationshipType": "many-to-one"}, {"otherEntityName": "ownerBankInfo", "otherEntityRelationshipName": "bankAccount", "relationshipName": "ownerBankInfo", "relationshipType": "one-to-many"}, {"otherEntityName": "reconTransaction", "otherEntityRelationshipName": "bankAccount", "relationshipName": "reconTransaction", "relationshipType": "one-to-many"}, {"otherEntityName": "nonReconTransaction", "otherEntityRelationshipName": "bankAccount", "relationshipName": "nonReconTransaction", "relationshipType": "one-to-many"}, {"otherEntityName": "voucherPayment", "otherEntityRelationshipName": "bankAccount", "relationshipName": "voucherPayment", "relationshipType": "one-to-many"}], "service": "serviceImpl"}