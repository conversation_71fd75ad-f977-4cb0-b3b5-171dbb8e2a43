{"applications": ["Escrow"], "changelogDate": "20220330114724", "dto": "mapstruct", "embedded": false, "entityTableName": "payment_plan_installment", "fields": [{"fieldName": "installmentNumber", "fieldType": "Integer"}, {"fieldName": "installmentDate", "fieldType": "ZonedDateTime"}, {"fieldName": "installmentDisplay", "fieldType": "String"}, {"fieldName": "installmentAmount", "fieldType": "Double"}, {"fieldName": "installmentPercentage", "fieldType": "Integer"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "PaymentPlanInstallment", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "paymentPlanInstallment", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "paymentPlanType", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "paymentPlanInstallment", "relationshipName": "unit", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "paymentPlanInstallment", "relationshipName": "project", "relationshipType": "many-to-one"}], "service": "serviceImpl"}