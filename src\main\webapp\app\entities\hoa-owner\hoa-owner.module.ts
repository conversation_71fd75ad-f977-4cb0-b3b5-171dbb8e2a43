import { NgModule } from '@angular/core';
import { SharedModule } from 'app/shared/shared.module';
import { HoaOwnerComponent } from './list/hoa-owner.component';
import { HoaOwnerDetailComponent } from './detail/hoa-owner-detail.component';
import { HoaOwnerUpdateComponent } from './update/hoa-owner-update.component';
import { HoaOwnerDeleteDialogComponent } from './delete/hoa-owner-delete-dialog.component';
import { HoaOwnerRoutingModule } from './route/hoa-owner-routing.module';

@NgModule({
  imports: [SharedModule, HoaOwnerRoutingModule],
  declarations: [HoaOwnerComponent, HoaOwnerDetailComponent, HoaOwnerUpdateComponent, HoaOwnerDeleteDialogComponent],
})
export class HoaOwnerModule {}
