import { Injectable } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { Resolve, ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of, EMPTY } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IAppOption } from '../app-option.model';
import { AppOptionService } from '../service/app-option.service';

@Injectable({ providedIn: 'root' })
export class AppOptionRoutingResolveService implements Resolve<IAppOption | null> {
  constructor(protected service: AppOptionService, protected router: Router) {}

  resolve(route: ActivatedRouteSnapshot): Observable<IAppOption | null | never> {
    const id = route.params['id'];
    if (id) {
      return this.service.find(id).pipe(
        mergeMap((appOption: HttpResponse<IAppOption>) => {
          if (appOption.body) {
            return of(appOption.body);
          } else {
            this.router.navigate(['404']);
            return EMPTY;
          }
        })
      );
    }
    return of(null);
  }
}
