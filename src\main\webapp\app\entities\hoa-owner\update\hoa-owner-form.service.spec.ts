import { TestBed } from '@angular/core/testing';

import { sampleWithRequiredData, sampleWithNewData } from '../hoa-owner.test-samples';

import { HoaOwnerFormService } from './hoa-owner-form.service';

describe('HoaOwner Form Service', () => {
  let service: HoaOwnerFormService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(HoaOwnerFormService);
  });

  describe('Service methods', () => {
    describe('createHoaOwnerFormGroup', () => {
      it('should create a new form with FormControl', () => {
        const formGroup = service.createHoaOwnerFormGroup();

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            id: expect.any(Object),
            ownerId: expect.any(Object),
            name: expect.any(Object),
            middleName: expect.any(Object),
            lastName: expect.any(Object),
            ownershipPercentage: expect.any(Object),
            idNo: expect.any(Object),
            contactTel: expect.any(Object),
            mobile: expect.any(Object),
            email: expect.any(Object),
            ownerNumber: expect.any(Object),
            isCurrent: expect.any(Object),
            idExpiaryDate: expect.any(Object),
            localeName: expect.any(Object),
            reservepercentage: expect.any(Object),
            unitMollacId: expect.any(Object),
            noofBedroom: expect.any(Object),
            floor: expect.any(Object),
            hoaCountryOption: expect.any(Object),
            hoaInvesterType: expect.any(Object),
            hoaInvesterIdType: expect.any(Object),
            hoaUnit: expect.any(Object),
            workFlowStates: expect.any(Object),
          })
        );
      });

      it('passing IHoaOwner should create a new form with FormGroup', () => {
        const formGroup = service.createHoaOwnerFormGroup(sampleWithRequiredData);

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            id: expect.any(Object),
            ownerId: expect.any(Object),
            name: expect.any(Object),
            middleName: expect.any(Object),
            lastName: expect.any(Object),
            ownershipPercentage: expect.any(Object),
            idNo: expect.any(Object),
            contactTel: expect.any(Object),
            mobile: expect.any(Object),
            email: expect.any(Object),
            ownerNumber: expect.any(Object),
            isCurrent: expect.any(Object),
            idExpiaryDate: expect.any(Object),
            localeName: expect.any(Object),
            reservepercentage: expect.any(Object),
            unitMollacId: expect.any(Object),
            noofBedroom: expect.any(Object),
            floor: expect.any(Object),
            hoaCountryOption: expect.any(Object),
            hoaInvesterType: expect.any(Object),
            hoaInvesterIdType: expect.any(Object),
            hoaUnit: expect.any(Object),
            workFlowStates: expect.any(Object),
          })
        );
      });
    });

    describe('getHoaOwner', () => {
      it('should return NewHoaOwner for default HoaOwner initial value', () => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const formGroup = service.createHoaOwnerFormGroup(sampleWithNewData);

        const hoaOwner = service.getHoaOwner(formGroup) as any;

        expect(hoaOwner).toMatchObject(sampleWithNewData);
      });

      it('should return NewHoaOwner for empty HoaOwner initial value', () => {
        const formGroup = service.createHoaOwnerFormGroup();

        const hoaOwner = service.getHoaOwner(formGroup) as any;

        expect(hoaOwner).toMatchObject({});
      });

      it('should return IHoaOwner', () => {
        const formGroup = service.createHoaOwnerFormGroup(sampleWithRequiredData);

        const hoaOwner = service.getHoaOwner(formGroup) as any;

        expect(hoaOwner).toMatchObject(sampleWithRequiredData);
      });
    });

    describe('resetForm', () => {
      it('passing IHoaOwner should not enable id FormControl', () => {
        const formGroup = service.createHoaOwnerFormGroup();
        expect(formGroup.controls.id.disabled).toBe(true);

        service.resetForm(formGroup, sampleWithRequiredData);

        expect(formGroup.controls.id.disabled).toBe(true);
      });

      it('passing NewHoaOwner should disable id FormControl', () => {
        const formGroup = service.createHoaOwnerFormGroup(sampleWithRequiredData);
        expect(formGroup.controls.id.disabled).toBe(true);

        service.resetForm(formGroup, { id: null });

        expect(formGroup.controls.id.disabled).toBe(true);
      });
    });
  });
});
