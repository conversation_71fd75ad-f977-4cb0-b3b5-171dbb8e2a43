{"applications": ["Escrow"], "changelogDate": "20220330111924", "dto": "mapstruct", "embedded": false, "entityTableName": "app_system_groups", "fields": [{"fieldName": "groupId", "fieldType": "String"}, {"fieldName": "labelId", "fieldType": "String"}, {"fieldName": "isEnabled", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "AppSystemGroups", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceImpl"}