{"applications": ["Escrow"], "changelogDate": "20220330114524", "dto": "mapstruct", "embedded": false, "entityTableName": "approval_sequence", "fields": [{"fieldName": "groupId", "fieldType": "String"}, {"fieldName": "teamSequence", "fieldType": "Integer"}, {"fieldName": "toAmount", "fieldType": "Double"}, {"fieldName": "fromAmount", "fieldType": "Double"}, {"fieldName": "isPayment", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "ApprovalSequence", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "approvalSequence", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}