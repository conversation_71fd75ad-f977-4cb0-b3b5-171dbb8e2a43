import dayjs from 'dayjs/esm';
import { IAppOption } from 'app/entities/app-option/app-option.model';
import { IHoaUnit } from 'app/entities/hoa-unit/hoa-unit.model';
import { IWorkFlowStates } from 'app/entities/work-flow-states/work-flow-states.model';

export interface IHoaOwner {
  id: number;
  ownerId?: string | null;
  name?: string | null;
  middleName?: string | null;
  lastName?: string | null;
  ownershipPercentage?: number | null;
  idNo?: string | null;
  contactTel?: string | null;
  mobile?: string | null;
  email?: string | null;
  ownerNumber?: number | null;
  isCurrent?: boolean | null;
  idExpiaryDate?: dayjs.Dayjs | null;
  localeName?: string | null;
  reservepercentage?: number | null;
  unitMollacId?: string | null;
  noofBedroom?: string | null;
  floor?: string | null;
  hoaCountryOption?: Pick<IAppOption, 'id'> | null;
  hoaInvesterType?: Pick<IAppOption, 'id'> | null;
  hoaInvesterIdType?: Pick<IAppOption, 'id'> | null;
  hoaUnit?: Pick<IHoaUnit, 'id'> | null;
  workFlowStates?: Pick<IWorkFlowStates, 'id'> | null;
}

export type NewHoaOwner = Omit<IHoaOwner, 'id'> & { id: null };
