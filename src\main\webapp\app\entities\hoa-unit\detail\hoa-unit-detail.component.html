<div class="d-flex justify-content-center">
  <div class="col-8">
    <div *ngIf="hoaUnit">
      <h2 data-cy="hoaUnitDetailsHeading"><span>Hoa Unit</span></h2>

      <hr />

      <ab-alert-error></ab-alert-error>

      <ab-alert></ab-alert>

      <dl class="row-md jh-entity-details">
        <dt><span>ID</span></dt>
        <dd>
          <span>{{ hoaUnit.id }}</span>
        </dd>
        <dt><span>Unit No</span></dt>
        <dd>
          <span>{{ hoaUnit.unitNo }}</span>
        </dd>
        <dt><span>Unit Ref Id</span></dt>
        <dd>
          <span>{{ hoaUnit.unitRefId }}</span>
        </dd>
        <dt><span>Alt Unit Ref Id</span></dt>
        <dd>
          <span>{{ hoaUnit.altUnitRefId }}</span>
        </dd>
        <dt><span>Name</span></dt>
        <dd>
          <span>{{ hoaUnit.name }}</span>
        </dd>
        <dt><span>Is Resale</span></dt>
        <dd>
          <span>{{ hoaUnit.isResale }}</span>
        </dd>
        <dt><span>Resale Date</span></dt>
        <dd>
          <span>{{ hoaUnit.resaleDate | formatMediumDatetime }}</span>
        </dd>
        <dt><span>Unit Sys Id</span></dt>
        <dd>
          <span>{{ hoaUnit.unitSysId }}</span>
        </dd>
        <dt><span>Other Format Unit No</span></dt>
        <dd>
          <span>{{ hoaUnit.otherFormatUnitNo }}</span>
        </dd>
        <dt><span>Virtual Acc No</span></dt>
        <dd>
          <span>{{ hoaUnit.virtualAccNo }}</span>
        </dd>
        <dt><span>Tower Name</span></dt>
        <dd>
          <span>{{ hoaUnit.towerName }}</span>
        </dd>
        <dt><span>Unit Plot Size</span></dt>
        <dd>
          <span>{{ hoaUnit.unitPlotSize }}</span>
        </dd>
        <dt><span>Floor</span></dt>
        <dd>
          <span>{{ hoaUnit.floor }}</span>
        </dd>
        <dt><span>Noof Bedroom</span></dt>
        <dd>
          <span>{{ hoaUnit.noofBedroom }}</span>
        </dd>
        <dt><span>Unit Iban</span></dt>
        <dd>
          <span>{{ hoaUnit.unitIban }}</span>
        </dd>
        <dt><span>Hoa Parent</span></dt>
        <dd>
          <div *ngIf="hoaUnit.hoaParent">
            <a [routerLink]="['/hoa-unit', hoaUnit.hoaParent.id, 'view']">{{ hoaUnit.hoaParent.id }}</a>
          </div>
        </dd>
        <dt><span>Hoa Unit Type</span></dt>
        <dd>
          <div *ngIf="hoaUnit.hoaUnitType">
            <a [routerLink]="['/hoa-unit-type', hoaUnit.hoaUnitType.id, 'view']">{{ hoaUnit.hoaUnitType.id }}</a>
          </div>
        </dd>
        <dt><span>Property</span></dt>
        <dd>
          <div *ngIf="hoaUnit.property">
            <a [routerLink]="['/property', hoaUnit.property.id, 'view']">{{ hoaUnit.property.id }}</a>
          </div>
        </dd>
        <dt><span>Hoa Unit Status</span></dt>
        <dd>
          <div *ngIf="hoaUnit.hoaUnitStatus">
            <a [routerLink]="['/app-option', hoaUnit.hoaUnitStatus.id, 'view']">{{ hoaUnit.hoaUnitStatus.id }}</a>
          </div>
        </dd>
        <dt><span>Property Id</span></dt>
        <dd>
          <div *ngIf="hoaUnit.propertyId">
            <a [routerLink]="['/app-option', hoaUnit.propertyId.id, 'view']">{{ hoaUnit.propertyId.id }}</a>
          </div>
        </dd>
        <dt><span>Hoa Credit Currency</span></dt>
        <dd>
          <div *ngIf="hoaUnit.hoaCreditCurrency">
            <a [routerLink]="['/app-option', hoaUnit.hoaCreditCurrency.id, 'view']">{{ hoaUnit.hoaCreditCurrency.id }}</a>
          </div>
        </dd>
        <dt><span>Hoa Prch Price Cur</span></dt>
        <dd>
          <div *ngIf="hoaUnit.hoaPrchPriceCur">
            <a [routerLink]="['/app-option', hoaUnit.hoaPrchPriceCur.id, 'view']">{{ hoaUnit.hoaPrchPriceCur.id }}</a>
          </div>
        </dd>
        <dt><span>Hoa Unit Pmt Plan Type</span></dt>
        <dd>
          <div *ngIf="hoaUnit.hoaUnitPmtPlanType">
            <a [routerLink]="['/app-option', hoaUnit.hoaUnitPmtPlanType.id, 'view']">{{ hoaUnit.hoaUnitPmtPlanType.id }}</a>
          </div>
        </dd>
        <dt><span>Work Flow States</span></dt>
        <dd>
          <div *ngIf="hoaUnit.workFlowStates">
            <a [routerLink]="['/work-flow-states', hoaUnit.workFlowStates.id, 'view']">{{ hoaUnit.workFlowStates.id }}</a>
          </div>
        </dd>
      </dl>

      <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
        <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span>Back</span>
      </button>

      <button type="button" [routerLink]="['/hoa-unit', hoaUnit.id, 'edit']" class="btn btn-primary">
        <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span>Edit</span>
      </button>
    </div>
  </div>
</div>
