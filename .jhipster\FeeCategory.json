{"applications": ["Escrow"], "changelogDate": "20220907104603", "dto": "mapstruct", "embedded": false, "entityTableName": "fee_category", "fields": [{"fieldName": "labelId", "fieldType": "String"}, {"fieldName": "categoryId", "fieldType": "String"}, {"fieldName": "cappedAmount", "fieldType": "Double"}, {"fieldName": "vatPercentage", "fieldType": "Double"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "FeeCategory", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "otherEntityRelationshipName": "proj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerSide": true, "relationshipName": "paymentFeeFrequency", "relationshipType": "many-to-many"}], "service": "serviceImpl"}