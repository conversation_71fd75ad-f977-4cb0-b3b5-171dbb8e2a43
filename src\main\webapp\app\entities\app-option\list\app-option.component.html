<div>
  <h2 id="page-heading" data-cy="AppOptionHeading">
    <span>App Options</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [spin]="isLoading"></fa-icon>
        <span>Refresh list</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-app-option"
        [routerLink]="['/app-option/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span> Create a new App Option </span>
      </button>
    </div>
  </h2>

  <ab-alert-error></ab-alert-error>

  <ab-alert></ab-alert>

  <ab-filter [filters]="filters"></ab-filter>

  <div class="alert alert-warning" id="no-result" *ngIf="appOptions?.length === 0">
    <span>No App Options found</span>
  </div>

  <div class="table-responsive table-entities" id="entities" *ngIf="appOptions && appOptions.length > 0">
    <table class="table table-striped" aria-describedby="page-heading">
      <thead>
        <tr abSort [(predicate)]="predicate" [(ascending)]="ascending" (sortChange)="navigateToWithComponentValues()">
          <th scope="col" abSortBy="id">
            <div class="d-flex">
              <span>ID</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="optionKey">
            <div class="d-flex">
              <span>Option Key</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="optionValue">
            <div class="d-flex">
              <span>Option Value</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="labelId">
            <div class="d-flex">
              <span>Label Id</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="description">
            <div class="d-flex">
              <span>Description</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col"></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let appOption of appOptions; trackBy: trackId" data-cy="entityTable">
          <td>
            <a [routerLink]="['/app-option', appOption.id, 'view']">{{ appOption.id }}</a>
          </td>
          <td>{{ appOption.optionKey }}</td>
          <td>{{ appOption.optionValue }}</td>
          <td>{{ appOption.labelId }}</td>
          <td>{{ appOption.description }}</td>
          <td class="text-end">
            <div class="btn-group">
              <button
                type="submit"
                [routerLink]="['/project']"
                [queryParams]="{ 'filter[bucketTypeId.in]': appOption.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Project</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/project']"
                [queryParams]="{ 'filter[blockPayementTypeId.in]': appOption.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Project</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/app-option']"
                [queryParams]="{ 'filter[paymentFeeFrequencyId.in]': appOption.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show App Option</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/property']"
                [queryParams]="{ 'filter[blockPayementTypeId.in]': appOption.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Property</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/app-option', appOption.id, 'view']"
                class="btn btn-info btn-sm"
                data-cy="entityDetailsButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">View</span>
              </button>

              <button
                type="submit"
                [routerLink]="['/app-option', appOption.id, 'edit']"
                class="btn btn-primary btn-sm"
                data-cy="entityEditButton"
              >
                <fa-icon icon="pencil-alt"></fa-icon>
                <span class="d-none d-md-inline">Edit</span>
              </button>

              <button type="submit" (click)="delete(appOption)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                <fa-icon icon="times"></fa-icon>
                <span class="d-none d-md-inline">Delete</span>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div *ngIf="appOptions && appOptions.length > 0">
    <div class="d-flex justify-content-center">
      <ab-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></ab-item-count>
    </div>

    <div class="d-flex justify-content-center">
      <ngb-pagination
        [collectionSize]="totalItems"
        [page]="page"
        [pageSize]="itemsPerPage"
        [maxSize]="5"
        [rotate]="true"
        [boundaryLinks]="true"
        (pageChange)="navigateToPage($event)"
      ></ngb-pagination>
    </div>
  </div>
</div>
