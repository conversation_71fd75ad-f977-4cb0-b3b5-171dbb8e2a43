{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "owner_bank_info", "fields": [{"fieldName": "payeeName", "fieldType": "String"}, {"fieldName": "payee<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "bankName", "fieldType": "String"}, {"fieldName": "bankAddress", "fieldType": "String"}, {"fieldName": "bicCode", "fieldType": "String"}, {"fieldName": "beneRoutingCode", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "OwnerBankInfo", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "bankAccount", "otherEntityRelationshipName": "ownerBankInfo", "relationshipName": "bankAccount", "relationshipType": "many-to-one"}, {"otherEntityName": "owner", "otherEntityRelationshipName": "ownerBankInfo", "relationshipName": "owner", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "payMode", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "ownerBankInfo", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}