import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { of, Subject, from } from 'rxjs';

import { HoaOwnerFormService } from './hoa-owner-form.service';
import { HoaOwnerService } from '../service/hoa-owner.service';
import { IHoaOwner } from '../hoa-owner.model';
import { IAppOption } from 'app/entities/app-option/app-option.model';
import { AppOptionService } from 'app/entities/app-option/service/app-option.service';
import { IHoaUnit } from 'app/entities/hoa-unit/hoa-unit.model';
import { HoaUnitService } from 'app/entities/hoa-unit/service/hoa-unit.service';
import { IWorkFlowStates } from 'app/entities/work-flow-states/work-flow-states.model';
import { WorkFlowStatesService } from 'app/entities/work-flow-states/service/work-flow-states.service';

import { HoaOwnerUpdateComponent } from './hoa-owner-update.component';

describe('HoaOwner Management Update Component', () => {
  let comp: HoaOwnerUpdateComponent;
  let fixture: ComponentFixture<HoaOwnerUpdateComponent>;
  let activatedRoute: ActivatedRoute;
  let hoaOwnerFormService: HoaOwnerFormService;
  let hoaOwnerService: HoaOwnerService;
  let appOptionService: AppOptionService;
  let hoaUnitService: HoaUnitService;
  let workFlowStatesService: WorkFlowStatesService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterTestingModule.withRoutes([])],
      declarations: [HoaOwnerUpdateComponent],
      providers: [
        FormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            params: from([{}]),
          },
        },
      ],
    })
      .overrideTemplate(HoaOwnerUpdateComponent, '')
      .compileComponents();

    fixture = TestBed.createComponent(HoaOwnerUpdateComponent);
    activatedRoute = TestBed.inject(ActivatedRoute);
    hoaOwnerFormService = TestBed.inject(HoaOwnerFormService);
    hoaOwnerService = TestBed.inject(HoaOwnerService);
    appOptionService = TestBed.inject(AppOptionService);
    hoaUnitService = TestBed.inject(HoaUnitService);
    workFlowStatesService = TestBed.inject(WorkFlowStatesService);

    comp = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('Should call AppOption query and add missing value', () => {
      const hoaOwner: IHoaOwner = { id: 456 };
      const hoaCountryOption: IAppOption = { id: 12628 };
      hoaOwner.hoaCountryOption = hoaCountryOption;
      const hoaInvesterType: IAppOption = { id: 66033 };
      hoaOwner.hoaInvesterType = hoaInvesterType;
      const hoaInvesterIdType: IAppOption = { id: 93584 };
      hoaOwner.hoaInvesterIdType = hoaInvesterIdType;

      const appOptionCollection: IAppOption[] = [{ id: 13246 }];
      jest.spyOn(appOptionService, 'query').mockReturnValue(of(new HttpResponse({ body: appOptionCollection })));
      const additionalAppOptions = [hoaCountryOption, hoaInvesterType, hoaInvesterIdType];
      const expectedCollection: IAppOption[] = [...additionalAppOptions, ...appOptionCollection];
      jest.spyOn(appOptionService, 'addAppOptionToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ hoaOwner });
      comp.ngOnInit();

      expect(appOptionService.query).toHaveBeenCalled();
      expect(appOptionService.addAppOptionToCollectionIfMissing).toHaveBeenCalledWith(
        appOptionCollection,
        ...additionalAppOptions.map(expect.objectContaining)
      );
      expect(comp.appOptionsSharedCollection).toEqual(expectedCollection);
    });

    it('Should call HoaUnit query and add missing value', () => {
      const hoaOwner: IHoaOwner = { id: 456 };
      const hoaUnit: IHoaUnit = { id: 7448 };
      hoaOwner.hoaUnit = hoaUnit;

      const hoaUnitCollection: IHoaUnit[] = [{ id: 42721 }];
      jest.spyOn(hoaUnitService, 'query').mockReturnValue(of(new HttpResponse({ body: hoaUnitCollection })));
      const additionalHoaUnits = [hoaUnit];
      const expectedCollection: IHoaUnit[] = [...additionalHoaUnits, ...hoaUnitCollection];
      jest.spyOn(hoaUnitService, 'addHoaUnitToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ hoaOwner });
      comp.ngOnInit();

      expect(hoaUnitService.query).toHaveBeenCalled();
      expect(hoaUnitService.addHoaUnitToCollectionIfMissing).toHaveBeenCalledWith(
        hoaUnitCollection,
        ...additionalHoaUnits.map(expect.objectContaining)
      );
      expect(comp.hoaUnitsSharedCollection).toEqual(expectedCollection);
    });

    it('Should call WorkFlowStates query and add missing value', () => {
      const hoaOwner: IHoaOwner = { id: 456 };
      const workFlowStates: IWorkFlowStates = { id: 13686 };
      hoaOwner.workFlowStates = workFlowStates;

      const workFlowStatesCollection: IWorkFlowStates[] = [{ id: 85758 }];
      jest.spyOn(workFlowStatesService, 'query').mockReturnValue(of(new HttpResponse({ body: workFlowStatesCollection })));
      const additionalWorkFlowStates = [workFlowStates];
      const expectedCollection: IWorkFlowStates[] = [...additionalWorkFlowStates, ...workFlowStatesCollection];
      jest.spyOn(workFlowStatesService, 'addWorkFlowStatesToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ hoaOwner });
      comp.ngOnInit();

      expect(workFlowStatesService.query).toHaveBeenCalled();
      expect(workFlowStatesService.addWorkFlowStatesToCollectionIfMissing).toHaveBeenCalledWith(
        workFlowStatesCollection,
        ...additionalWorkFlowStates.map(expect.objectContaining)
      );
      expect(comp.workFlowStatesSharedCollection).toEqual(expectedCollection);
    });

    it('Should update editForm', () => {
      const hoaOwner: IHoaOwner = { id: 456 };
      const hoaCountryOption: IAppOption = { id: 88320 };
      hoaOwner.hoaCountryOption = hoaCountryOption;
      const hoaInvesterType: IAppOption = { id: 568 };
      hoaOwner.hoaInvesterType = hoaInvesterType;
      const hoaInvesterIdType: IAppOption = { id: 62738 };
      hoaOwner.hoaInvesterIdType = hoaInvesterIdType;
      const hoaUnit: IHoaUnit = { id: 55911 };
      hoaOwner.hoaUnit = hoaUnit;
      const workFlowStates: IWorkFlowStates = { id: 396 };
      hoaOwner.workFlowStates = workFlowStates;

      activatedRoute.data = of({ hoaOwner });
      comp.ngOnInit();

      expect(comp.appOptionsSharedCollection).toContain(hoaCountryOption);
      expect(comp.appOptionsSharedCollection).toContain(hoaInvesterType);
      expect(comp.appOptionsSharedCollection).toContain(hoaInvesterIdType);
      expect(comp.hoaUnitsSharedCollection).toContain(hoaUnit);
      expect(comp.workFlowStatesSharedCollection).toContain(workFlowStates);
      expect(comp.hoaOwner).toEqual(hoaOwner);
    });
  });

  describe('save', () => {
    it('Should call update service on save for existing entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IHoaOwner>>();
      const hoaOwner = { id: 123 };
      jest.spyOn(hoaOwnerFormService, 'getHoaOwner').mockReturnValue(hoaOwner);
      jest.spyOn(hoaOwnerService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ hoaOwner });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: hoaOwner }));
      saveSubject.complete();

      // THEN
      expect(hoaOwnerFormService.getHoaOwner).toHaveBeenCalled();
      expect(comp.previousState).toHaveBeenCalled();
      expect(hoaOwnerService.update).toHaveBeenCalledWith(expect.objectContaining(hoaOwner));
      expect(comp.isSaving).toEqual(false);
    });

    it('Should call create service on save for new entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IHoaOwner>>();
      const hoaOwner = { id: 123 };
      jest.spyOn(hoaOwnerFormService, 'getHoaOwner').mockReturnValue({ id: null });
      jest.spyOn(hoaOwnerService, 'create').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ hoaOwner: null });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: hoaOwner }));
      saveSubject.complete();

      // THEN
      expect(hoaOwnerFormService.getHoaOwner).toHaveBeenCalled();
      expect(hoaOwnerService.create).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).toHaveBeenCalled();
    });

    it('Should set isSaving to false on error', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IHoaOwner>>();
      const hoaOwner = { id: 123 };
      jest.spyOn(hoaOwnerService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ hoaOwner });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.error('This is an error!');

      // THEN
      expect(hoaOwnerService.update).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).not.toHaveBeenCalled();
    });
  });

  describe('Compare relationships', () => {
    describe('compareAppOption', () => {
      it('Should forward to appOptionService', () => {
        const entity = { id: 123 };
        const entity2 = { id: 456 };
        jest.spyOn(appOptionService, 'compareAppOption');
        comp.compareAppOption(entity, entity2);
        expect(appOptionService.compareAppOption).toHaveBeenCalledWith(entity, entity2);
      });
    });

    describe('compareHoaUnit', () => {
      it('Should forward to hoaUnitService', () => {
        const entity = { id: 123 };
        const entity2 = { id: 456 };
        jest.spyOn(hoaUnitService, 'compareHoaUnit');
        comp.compareHoaUnit(entity, entity2);
        expect(hoaUnitService.compareHoaUnit).toHaveBeenCalledWith(entity, entity2);
      });
    });

    describe('compareWorkFlowStates', () => {
      it('Should forward to workFlowStatesService', () => {
        const entity = { id: 123 };
        const entity2 = { id: 456 };
        jest.spyOn(workFlowStatesService, 'compareWorkFlowStates');
        comp.compareWorkFlowStates(entity, entity2);
        expect(workFlowStatesService.compareWorkFlowStates).toHaveBeenCalledWith(entity, entity2);
      });
    });
  });
});
