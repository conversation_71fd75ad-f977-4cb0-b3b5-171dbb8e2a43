{"applications": ["Escrow"], "changelogDate": "20220330112724", "dto": "mapstruct", "embedded": false, "entityTableName": "developer_beneficiary", "fields": [{"fieldName": "beneficiaryId", "fieldType": "String"}, {"fieldName": "beneficiaryType", "fieldType": "String"}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "isActive", "fieldType": "Boolean"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "DeveloperBeneficiary", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "relationshipName": "devBeneTranferType", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "developerBeneficiary", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "developer", "otherEntityRelationshipName": "developerBeneficiary", "ownerSide": true, "relationshipName": "developer", "relationshipType": "many-to-many"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "developerBeneficiary", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}], "service": "serviceImpl"}