{"applications": ["Escrow"], "changelogDate": "20230129131849", "dto": "mapstruct", "embedded": false, "entityTableName": "hoa_owner", "fields": [{"fieldName": "ownerId", "fieldType": "String"}, {"fieldName": "name", "fieldType": "String"}, {"fieldName": "middleName", "fieldType": "String"}, {"fieldName": "lastName", "fieldType": "String"}, {"fieldName": "ownershipPercentage", "fieldType": "Double"}, {"fieldName": "idNo", "fieldType": "String"}, {"fieldName": "contactTel", "fieldType": "String"}, {"fieldName": "mobile", "fieldType": "String"}, {"fieldName": "email", "fieldType": "String"}, {"fieldName": "ownerNumber", "fieldType": "Integer"}, {"fieldName": "isCurrent", "fieldType": "Boolean"}, {"fieldName": "idExpiaryDate", "fieldType": "ZonedDateTime"}, {"fieldName": "localeName", "fieldType": "String"}, {"fieldName": "reservepercentage", "fieldType": "Double"}, {"fieldName": "unitMollacId", "fieldType": "String"}, {"fieldName": "noofBedroom", "fieldType": "String"}, {"fieldName": "floor", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "relationshipName": "hoaCountryOption", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "hoaInvesterType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "hoaInvesterIdType", "relationshipType": "many-to-one"}, {"otherEntityName": "hoaUnit", "otherEntityRelationshipName": "hoa<PERSON>wner", "relationshipName": "hoaUnit", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "hoa<PERSON>wner", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}