{"applications": ["Escrow"], "changelogDate": "20220909055701", "dto": "mapstruct", "embedded": false, "entityTableName": "app_module", "fields": [{"fieldName": "moduleId", "fieldType": "String"}, {"fieldName": "labelId", "fieldType": "String"}, {"fieldName": "priority", "fieldType": "Integer"}, {"fieldName": "entityName", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "AppModule", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceImpl"}