{"applications": ["Escrow"], "changelogDate": "20230531151156", "dto": "mapstruct", "embedded": false, "entityTableName": "balance_confirmation", "fields": [{"fieldName": "amount", "fieldType": "Double"}, {"fieldName": "fromDate", "fieldType": "ZonedDateTime"}, {"fieldName": "toDate", "fieldType": "ZonedDateTime"}, {"fieldName": "currentDate", "fieldType": "ZonedDateTime"}, {"fieldName": "isSent", "fieldType": "Boolean"}, {"fieldName": "specialField1", "fieldType": "String"}, {"fieldName": "specialField2", "fieldType": "String"}, {"fieldName": "specialField3", "fieldType": "String"}, {"fieldName": "specialField4", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "BalanceConfirmation", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "project", "otherEntityRelationshipName": "balanceConfirmation", "relationshipName": "project", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "balanceConfirmation", "relationshipName": "unit", "relationshipType": "many-to-one"}], "service": "serviceImpl"}