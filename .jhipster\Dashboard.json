{"applications": ["Escrow"], "changelogDate": "20220330115624", "dto": "mapstruct", "embedded": false, "entityTableName": "dashboard", "fields": [{"fieldName": "labelId", "fieldType": "String"}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "priority", "fieldType": "Integer"}, {"fieldName": "query", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "uid", "fieldType": "String"}, {"fieldName": "graphDefination", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "tableId", "fieldType": "String"}, {"fieldName": "isEnabled", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Dashboard", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceImpl"}