import { Injectable } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { Resolve, ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of, EMPTY } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IHoaUnit } from '../hoa-unit.model';
import { HoaUnitService } from '../service/hoa-unit.service';

@Injectable({ providedIn: 'root' })
export class HoaUnitRoutingResolveService implements Resolve<IHoaUnit | null> {
  constructor(protected service: HoaUnitService, protected router: Router) {}

  resolve(route: ActivatedRouteSnapshot): Observable<IHoaUnit | null | never> {
    const id = route.params['id'];
    if (id) {
      return this.service.find(id).pipe(
        mergeMap((hoaUnit: HttpResponse<IHoaUnit>) => {
          if (hoaUnit.body) {
            return of(hoaUnit.body);
          } else {
            this.router.navigate(['404']);
            return EMPTY;
          }
        })
      );
    }
    return of(null);
  }
}
