application {
  config {
    applicationType monolith,
    authenticationType oauth2,
    baseName Escrow,
    blueprints [],
    buildTool maven,
    cacheProvider ehcache,
    clientFramework angularX,
    clientPackageManager npm,
    creationTimestamp 1648638684422,
    databaseType sql,
    devDatabaseType h2Disk,
    dtoSuffix DTO,
    enableHibernateCache true,
    enableSwaggerCodegen false,
    enableTranslation false,
    jhiPrefix ab,
    languages [en],
    messageBroker false,
    nativeLanguage en,
    otherModules [],
    packageName com.ascentbusiness.escrow,
    prodDatabaseType mssql,
    reactive false,
    searchEngine false,
    serverPort 8080,
    serviceDiscoveryType no,
    skipUserManagement true,
    testFrameworks [],
    websocket false,
  }
  entities *
}

entity AppKeyValueStore{
	key String required
    value String required
	isReadonly Boolean
}

entity AppKeyBlobStore{
	key String required
    value TextBlob required
	isReadonly Boolean
}

entity AppTableDesign{
	name String required
    tableDefination TextBlob required
}

entity AppFormDesign{
	name String required
    formDefination TextBlob required
}

entity AppDocument{
	name String,
    location String,
    module String,
    recordId Long,
    storageType String
	uploadDate ZonedDateTime
	documentSize String
	validityDate ZonedDateTime
}

entity AppLocale{
	name String required
    isRtl Boolean required
}

entity AppLabel{
	uiId String required
    value String required
    helpContent String
}

relationship ManyToOne{	
    AppLabel{locale} to AppLocale{label}
	AppDocument{documentType} to AppOption
}

entity AppSystemGroups{
	groupId String
    labelId String
	isEnabled Boolean
}

entity AppOption{
	optionKey String
	optionValue String
	optionLabel String
	description TextBlob
}


entity Branch{
	name String required
    swift String
    routingCode String
    ttcCode String
    branchCode String required
}

relationship ManyToOne{
	Branch{bankOption} to AppOption
}

entity BankAccount{
	bankAccountNumber String
    bankAccountBalance Double
	ibanNo String
	openedDate ZonedDateTime
	accountTitle String
	currency String	
	swiftcode String
	routingCode String
}

relationship ManyToOne{
	BankAccount to Branch
}

entity MasterBankAccountType{
	labelId String 
    typeId String
}

entity ChildBankAccountType{
	labelId String 
    typeId String
}

relationship ManyToOne{
	ChildBankAccountType to MasterBankAccountType
}

relationship ManyToOne{
	BankAccount to MasterBankAccountType
    BankAccount to ChildBankAccountType
}

entity Developer{
	developerId String 
    cifRera String required maxlength(25)
	developerRegNo String maxlength(100)
	name String required maxlength(100)
	masterName String 
	nameLocal String
    onboardingDate ZonedDateTime required
    contactAddress String required maxlength(150)
    contactTel String required maxlength(20)
    poBox String 
    mobile String maxlength(20)
    fax String maxlength(20)
    email String
	licenseNo String
	licenseExpDate ZonedDateTime
	worldCheckFlag Boolean
	worldCheckRemarks String maxlength(100)
	migratedData Boolean
	remark TextBlob
}


entity DeveloperContact{
	contactName String 
	contactTelNo String 
	contactMobNo String
	contactEmail String
	contactAddress String	
	contactPoBox String
	contactFaxNo String
}
relationship ManyToOne{	
    Developer{regulator} to AppOption
    Developer{developerActiveStatus} to AppOption
	Developer to DeveloperContact
}

entity DeveloperBeneficiary{
	beneficiaryId String
    beneficiaryType String
	name String required maxlength(50)
}

relationship ManyToOne{
    BankAccount to DeveloperBeneficiary
}

relationship ManyToMany{
    DeveloperBeneficiary to Developer
}

entity Project{
	projectId String 	
    cif String required
    name String required
    nameLocal String
    location String required
    projectNumberRera String required
    startDate ZonedDateTime required
    completionDate ZonedDateTime required
    percentComplete Integer max(100)
    constructionCost Double
	accStatusDate ZonedDateTime
	registrationDate ZonedDateTime
	noOfUnits Integer
	remarks TextBlob
	specialApproval String	
	projectManagedBy String
	projectBackupUser String
}

relationship ManyToOne{
	Project to Developer
    Project{projectStatus} to AppOption
	Project{projectType} to AppOption
	Project{projectAccountStatus} to AppOption
	Project{constructionCostCurrency} to AppOption 	
}

relationship ManyToMany{
	Project{BucketType} to AppOption{projectOption}
}

entity ProjectFee{
	amount Double required
    vatPercentage Double
    totalAmount Double
}

relationship ManyToOne{
	ProjectFee to Project
    ProjectFee{projectFeeCategory} to AppOption  
	ProjectFee{projectFeecurrency} to AppOption 
	ProjectFee{paymentFrequency} to AppOption
}

entity ProjectBeneficiary{
	beneficiaryId String
	name String required
    contractAmount Double
    actualLandPrice Double
    contractorName String 
    contractAmount Double
	beneficiaryType String
	beneficiaryBank String
	beneficiarySwift String
	beneficiaryRoutingCode String
}

relationship ManyToOne{
    BankAccount to ProjectBeneficiary
	ProjectBeneficiary{tranferType} to AppOption
}

relationship ManyToMany{
    ProjectBeneficiary to Project
}

relationship ManyToOne{
    ProjectBeneficiary{expenseType} to AppOption
}


relationship ManyToOne{
    ProjectBeneficiary{pbVendorSubType} to AppOption
    ProjectBeneficiary{pbContractorSubType} to AppOption
	ProjectBeneficiary{pbInfrastructureCategory} to AppOption
    ProjectBeneficiary{pbSalesCategory} to AppOption
}


entity ProjectFinancial{
	estRevenue String
	estConstructionCost Double
	estProjectMgmtExpense Double
	estLandCost Double
	estMarketingExpense Double
	estimatedDate ZonedDateTime
	actualSoldValue Double
	actualConstructionCost Double
	actualInfraCost Double
	actualLandCost Double
	actualMarketingExp Double
	actualProjectMgmtExpense Double
	actualDate ZonedDateTime
	currentCashReceived Double
	currentLandCost Double
	currentConstructionCost Double
	currentMarketingExp Double
	currentProjectMgmtExp Double
	currentMortgage Double
	currentVatPayment Double
	currentOqood Double
	currentRefund Double
	currentBalInRetenAcc Double
	currentBalInTrustAcc Double
	currentTechnicalFee Double
	currentUnIdentifiedFund Double
	currentLoanInstal Double
	currentInfraCost Double
	currentOthersCost Double
	currentTransferredCost Double
	currentForfeitedCost Double
	currentDeveloperEquitycost Double
	currentAmantFund Double
	currentOtherWithdrawls Double
	currentOqoodOtherFeePay Double
	currentVatDeposit Double
	creditInterest Double
	paymentForRetentionAcc Double
	developerReimburse Double
	unitRegFees Double
	creditInterestProfit Double
	vatCappedCost Double	
}

entity ProjectClosure{
	totalIncomeFund Double
	totalPayment Double
	checkGuranteeDoc Boolean
}

relationship ManyToOne{
    ProjectFinancial to Developer
    ProjectFinancial to Project
	ProjectClosure to Project
	ProjectClosure to AppDocument
}


entity UnitType{
	name String required
    icon ImageBlob
	isStandalone Boolean required
	unitTypePrefix String
	excelFormula String
	jsFormula String
}

relationship ManyToOne{
	UnitType{parent} to UnitType{child}
}

entity Unit{
	unitRefId String
	name String
    isResale Boolean
    resaleDate ZonedDateTime
	unitSysId String
	otherFormatUnitNo String
	towerName String
	unitPlotSize String
}

relationship ManyToOne{	
	Unit{parent} to Unit{child}
	Unit to UnitType
    Unit to Project
	Unit{unitStatus} to AppOption
}

entity UnitPurchase{
	purchaseDate ZonedDateTime required
    saleRate Double required
    purchasePrice Double required
    unitRegistrationFee Double required
	agentName String
	agentId String
	grossSaleprice Double
	vatApplicable Boolean
	deedNo String
	agreementNo String
	agreementDate ZonedDateTime
	salePurchaseAgreement Boolean
	worldCheck Boolean
	amtPaidToDevInEscorw Double
	amtPaidToDevOutEscorw Double
	totalAmountPaid Double
}
entity UnitBooking{
	amountPaid Double
	areaSize Double
	forFeitAmount Double
	dldAmount Double
	refundAmount Double
	remarks TextBlob
	transferredAmount Double
}

relationship ManyToOne{
	UnitPurchase{creditCurrency} to AppOption 
    UnitPurchase{purchasePriceCurrency} to AppOption
	UnitPurchase to Unit
}

relationship ManyToOne{
	 BankAccount to Unit
	 Unit to PaymentPlanType
	 Unit to UnitBooking
}

entity Owner{
	ownerId String
    name String required
    middleName String
    lastName String required
    ownershipPercentage Float required
    idNo String required
    contactTel String required
    mobile String
    email String
    ownerNumber Integer
    isCurrent Boolean
	idExpiaryDate ZonedDateTime
	floor String
	noofBedroom String
}


entity OwnerBankDetail {
  	payeeName String
	payeeAddress String
	bankName String
	bankAddress String
	bicCode String
}

relationship ManyToOne{
	Owner{documentIdType} to AppOption
    Owner{countryOption} to AppOption
	OwnerBankDetail to BankAccount
}

relationship ManyToOne{
	Owner to Unit
}


relationship ManyToOne{
	BankAccount to Project
}

//gaurentee

entity GuaranteeType{
	labelId String, 
    typeId String,
    fetchUrl String
}

entity Guarantee{
	guaranteeReferenceNumber String required,
    guaranteeDate ZonedDateTime required,
    name String,
    openEnded Boolean,
    guaranteeExpirationDate ZonedDateTime,
    gaurenteeAmount Double
}

relationship ManyToOne{
	Guarantee to GuaranteeType
    Guarantee to Project
    Guarantee{issuerBank} to AppOption
}

entity GaurenteeRecovery{
	reductionAmount Double,
    balanceAmount Double
    
}

relationship ManyToOne{
	GaurenteeRecovery to Guarantee
}

entity GaurenteeReleaser{
	requestDate ZonedDateTime
}

relationship ManyToOne{
	GaurenteeReleaser to Guarantee,
	Project to WorkFlowStates
	Developer to WorkFlowStates
	Owner to WorkFlowStates
	Guarantee to WorkFlowStates
	Unit to WorkFlowStates
	GaurenteeRecovery to WorkFlowStates
	GaurenteeReleaser to WorkFlowStates
}



// Workflow
entity WorkFlowStates {
     desc String
     labelId String
     stateId String
     isEnabled Boolean
}

entity WorkFlowActivity{
    moduleName String
    moduleRefId String
    groupId String
    teamSequence Integer
    staffId String
    workFlowStatus Boolean
    comment String
    isEnabled Boolean 
    approvedDateTime ZonedDateTime
}
entity ApprovalSequence {
   	groupId String
   	teamSequence Integer
}

relationship OneToMany{ 
 	WorkFlowStates to WorkFlowActivity
}

enum PaymentPlanCategory{
    INSTALMENT,
    INSTALMENT_EDITABALE,
    COMPLETION_PERCENTAGE
}

entity PaymentPlanType{
    labelId String,
    typeId String,
    paymentPlanCategory PaymentPlanCategory
}

entity PaymentPlanInstallment{
    installmentNumber Integer,
    installmentDate ZonedDateTime,
    installmentDisplay String,
    installmentAmount Double,
    installmentPercentage Integer
}

entity PaymentPlanCompletion{
    installmentNumber Integer,
    projectCompletion Integer,
    installmentAmount Double,
    bufferMonth Integer,
    installmentPercentage Integer
}

relationship ManyToOne{
    PaymentPlanInstallment to PaymentPlanType
    PaymentPlanCompletion to PaymentPlanType
    PaymentPlanCompletion to Unit
    PaymentPlanInstallment to Unit
    PaymentPlanCompletion to Project
    PaymentPlanInstallment to Project
}

//Buckets

entity BucketRule{
	percentageToTranfer Integer,
    narration String
}

relationship ManyToOne{
	BucketRule{bucketType} to AppOption
    BucketRule to MasterBankAccountType
    BucketRule to ChildBankAccountType
}

//Transactions
entity TransactionStaging{
	transactionId String,
	projectCif String,
    description String,
    escrowAccountNumber String,
    amount String,
    creditDebit String,
    bucket String,
    unitRefNumber String,
    transactionDate ZonedDateTime,
    narration String
    specialField1 String,
    specialField2 String,
    specialField3 String,
    specialField4 String,
    specialField5 String
}


entity ReconTransaction{
	transactionId String,
	amount Double,
    transactionDate ZonedDateTime,
    narration String,
    description String
}


relationship ManyToOne{
	ReconTransaction to Project
    ReconTransaction to Unit
    ReconTransaction{bucketType} to AppOption
	ReconTransaction to BankAccount
}


entity NonReconTransaction{
	transactionId String,
	amount Double,
    transactionDate ZonedDateTime,
    narration String,
    description String
}

relationship ManyToOne{
	NonReconTransaction to Project
    NonReconTransaction to Unit
    NonReconTransaction{bucketType} to AppOption
	NonReconTransaction to BankAccount
}

entity BucketRuleTransaction{
parentTransactionId String
reason String
amount Double
transactionId String
transactionDate ZonedDateTime
narration String
description String
}

relationship ManyToOne{
BucketRuleTransaction{fromAccount} to BankAccount
BucketRuleTransaction{toAccount} to BankAccount
}



//Voucher Payment

entity VoucherPayment{
	invoiceNumber String,
    paymentDate ZonedDateTime,
    paymentAmount Double,
    glAccountNumber String,
    glBranchCode String,
    unitRegistrationFee String,
    remark String
}

relationship ManyToOne{
	VoucherPayment{voucherPaymentType} to AppOption
    VoucherPayment{expenseType} to AppOption
    VoucherPayment to Project
    VoucherPayment to BankAccount
    VoucherPayment to ProjectBeneficiary
}



//Check Return
entity ReturnCheck{
	chequeReceivingDate ZonedDateTime,
    checkNumber String,
    withdrawlName String,
    amount Double,
    otherReason String,
    checkReceived Boolean
}

relationship ManyToOne{
	ReturnCheck to Project
    ReturnCheck{bankOption} to AppOption
    ReturnCheck{returnCheckReasonType} to AppOption
    ReturnCheck to Unit
    
}


//Dashboard and Report

entity Dashboard{	
    labelId String
	description String
	priority Integer
	query TextBlob
	uid String
	graphDefination TextBlob
	tableId String
	isEnabled Boolean
}

entity Report{	
    labelId String
	description String
	uid String
	priority Integer
	query TextBlob
	tableId String
	isEnabled Boolean
}

paginate * with pagination

service * with serviceImpl

dto * with mapstruct

filter *
