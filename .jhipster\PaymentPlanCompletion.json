{"applications": ["Escrow"], "changelogDate": "20220330114824", "dto": "mapstruct", "embedded": false, "entityTableName": "payment_plan_completion", "fields": [{"fieldName": "installmentNumber", "fieldType": "Integer"}, {"fieldName": "projectCompletion", "fieldType": "String"}, {"fieldName": "installmentAmount", "fieldType": "Double"}, {"fieldName": "bufferMonth", "fieldType": "Integer"}, {"fieldName": "installmentPercentage", "fieldType": "String"}, {"fieldName": "installmentDate", "fieldType": "ZonedDateTime"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "PaymentPlanCompletion", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "paymentPlanCompletion", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "paymentPlanType", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "paymentPlanCompletion", "relationshipName": "unit", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "paymentPlanCompletion", "relationshipName": "project", "relationshipType": "many-to-one"}], "service": "serviceImpl"}