import { Injectable } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import dayjs from 'dayjs/esm';
import { DATE_TIME_FORMAT } from 'app/config/input.constants';
import { IHoaOwner, NewHoaOwner } from '../hoa-owner.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { id: unknown }> = Partial<Omit<T, 'id'>> & { id: T['id'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts IHoaOwner for edit and NewHoaOwnerFormGroupInput for create.
 */
type HoaOwnerFormGroupInput = IHoaOwner | PartialWithRequiredKeyOf<NewHoaOwner>;

/**
 * Type that converts some properties for forms.
 */
type FormValueOf<T extends IHoaOwner | NewHoaOwner> = Omit<T, 'idExpiaryDate'> & {
  idExpiaryDate?: string | null;
};

type HoaOwnerFormRawValue = FormValueOf<IHoaOwner>;

type NewHoaOwnerFormRawValue = FormValueOf<NewHoaOwner>;

type HoaOwnerFormDefaults = Pick<NewHoaOwner, 'id' | 'isCurrent' | 'idExpiaryDate'>;

type HoaOwnerFormGroupContent = {
  id: FormControl<HoaOwnerFormRawValue['id'] | NewHoaOwner['id']>;
  ownerId: FormControl<HoaOwnerFormRawValue['ownerId']>;
  name: FormControl<HoaOwnerFormRawValue['name']>;
  middleName: FormControl<HoaOwnerFormRawValue['middleName']>;
  lastName: FormControl<HoaOwnerFormRawValue['lastName']>;
  ownershipPercentage: FormControl<HoaOwnerFormRawValue['ownershipPercentage']>;
  idNo: FormControl<HoaOwnerFormRawValue['idNo']>;
  contactTel: FormControl<HoaOwnerFormRawValue['contactTel']>;
  mobile: FormControl<HoaOwnerFormRawValue['mobile']>;
  email: FormControl<HoaOwnerFormRawValue['email']>;
  ownerNumber: FormControl<HoaOwnerFormRawValue['ownerNumber']>;
  isCurrent: FormControl<HoaOwnerFormRawValue['isCurrent']>;
  idExpiaryDate: FormControl<HoaOwnerFormRawValue['idExpiaryDate']>;
  localeName: FormControl<HoaOwnerFormRawValue['localeName']>;
  reservepercentage: FormControl<HoaOwnerFormRawValue['reservepercentage']>;
  unitMollacId: FormControl<HoaOwnerFormRawValue['unitMollacId']>;
  noofBedroom: FormControl<HoaOwnerFormRawValue['noofBedroom']>;
  floor: FormControl<HoaOwnerFormRawValue['floor']>;
  hoaCountryOption: FormControl<HoaOwnerFormRawValue['hoaCountryOption']>;
  hoaInvesterType: FormControl<HoaOwnerFormRawValue['hoaInvesterType']>;
  hoaInvesterIdType: FormControl<HoaOwnerFormRawValue['hoaInvesterIdType']>;
  hoaUnit: FormControl<HoaOwnerFormRawValue['hoaUnit']>;
  workFlowStates: FormControl<HoaOwnerFormRawValue['workFlowStates']>;
};

export type HoaOwnerFormGroup = FormGroup<HoaOwnerFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class HoaOwnerFormService {
  createHoaOwnerFormGroup(hoaOwner: HoaOwnerFormGroupInput = { id: null }): HoaOwnerFormGroup {
    const hoaOwnerRawValue = this.convertHoaOwnerToHoaOwnerRawValue({
      ...this.getFormDefaults(),
      ...hoaOwner,
    });
    return new FormGroup<HoaOwnerFormGroupContent>({
      id: new FormControl(
        { value: hoaOwnerRawValue.id, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        }
      ),
      ownerId: new FormControl(hoaOwnerRawValue.ownerId),
      name: new FormControl(hoaOwnerRawValue.name),
      middleName: new FormControl(hoaOwnerRawValue.middleName),
      lastName: new FormControl(hoaOwnerRawValue.lastName),
      ownershipPercentage: new FormControl(hoaOwnerRawValue.ownershipPercentage),
      idNo: new FormControl(hoaOwnerRawValue.idNo),
      contactTel: new FormControl(hoaOwnerRawValue.contactTel),
      mobile: new FormControl(hoaOwnerRawValue.mobile),
      email: new FormControl(hoaOwnerRawValue.email),
      ownerNumber: new FormControl(hoaOwnerRawValue.ownerNumber),
      isCurrent: new FormControl(hoaOwnerRawValue.isCurrent),
      idExpiaryDate: new FormControl(hoaOwnerRawValue.idExpiaryDate),
      localeName: new FormControl(hoaOwnerRawValue.localeName),
      reservepercentage: new FormControl(hoaOwnerRawValue.reservepercentage),
      unitMollacId: new FormControl(hoaOwnerRawValue.unitMollacId),
      noofBedroom: new FormControl(hoaOwnerRawValue.noofBedroom),
      floor: new FormControl(hoaOwnerRawValue.floor),
      hoaCountryOption: new FormControl(hoaOwnerRawValue.hoaCountryOption),
      hoaInvesterType: new FormControl(hoaOwnerRawValue.hoaInvesterType),
      hoaInvesterIdType: new FormControl(hoaOwnerRawValue.hoaInvesterIdType),
      hoaUnit: new FormControl(hoaOwnerRawValue.hoaUnit),
      workFlowStates: new FormControl(hoaOwnerRawValue.workFlowStates),
    });
  }

  getHoaOwner(form: HoaOwnerFormGroup): IHoaOwner | NewHoaOwner {
    return this.convertHoaOwnerRawValueToHoaOwner(form.getRawValue() as HoaOwnerFormRawValue | NewHoaOwnerFormRawValue);
  }

  resetForm(form: HoaOwnerFormGroup, hoaOwner: HoaOwnerFormGroupInput): void {
    const hoaOwnerRawValue = this.convertHoaOwnerToHoaOwnerRawValue({ ...this.getFormDefaults(), ...hoaOwner });
    form.reset(
      {
        ...hoaOwnerRawValue,
        id: { value: hoaOwnerRawValue.id, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */
    );
  }

  private getFormDefaults(): HoaOwnerFormDefaults {
    const currentTime = dayjs();

    return {
      id: null,
      isCurrent: false,
      idExpiaryDate: currentTime,
    };
  }

  private convertHoaOwnerRawValueToHoaOwner(rawHoaOwner: HoaOwnerFormRawValue | NewHoaOwnerFormRawValue): IHoaOwner | NewHoaOwner {
    return {
      ...rawHoaOwner,
      idExpiaryDate: dayjs(rawHoaOwner.idExpiaryDate, DATE_TIME_FORMAT),
    };
  }

  private convertHoaOwnerToHoaOwnerRawValue(
    hoaOwner: IHoaOwner | (Partial<NewHoaOwner> & HoaOwnerFormDefaults)
  ): HoaOwnerFormRawValue | PartialWithRequiredKeyOf<NewHoaOwnerFormRawValue> {
    return {
      ...hoaOwner,
      idExpiaryDate: hoaOwner.idExpiaryDate ? hoaOwner.idExpiaryDate.format(DATE_TIME_FORMAT) : undefined,
    };
  }
}
