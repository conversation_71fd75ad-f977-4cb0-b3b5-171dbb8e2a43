{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "property_beneficiary", "fields": [{"fieldName": "beneficiaryId", "fieldType": "String"}, {"fieldName": "tradeLicenseNo", "fieldType": "String"}, {"fieldName": "tradeLicenseExpiaryDate", "fieldType": "ZonedDateTime"}, {"fieldName": "beneficiaryBank", "fieldType": "String"}, {"fieldName": "beneficiarySwift", "fieldType": "String"}, {"fieldName": "beneficiaryRoutingCode", "fieldType": "String"}, {"fieldName": "name", "fieldType": "String"}, {"fieldName": "isActive", "fieldType": "Boolean"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "PropertyBeneficiary", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "relationshipName": "placeOfIssue", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "tranferType", "relationshipType": "many-to-one"}, {"otherEntityName": "property", "otherEntityRelationshipName": "propertyBeneficiary", "relationshipName": "property", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "propertyBeneficiary", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "propertyBeneficiary", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}], "service": "serviceImpl"}