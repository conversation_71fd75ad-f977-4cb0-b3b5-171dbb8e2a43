{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "branch", "fields": [{"fieldName": "name", "fieldType": "String"}, {"fieldName": "swift", "fieldType": "String"}, {"fieldName": "routingCode", "fieldType": "String"}, {"fieldName": "ttcCode", "fieldType": "String"}, {"fieldName": "branchCode", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Branch", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "bank", "otherEntityRelationshipName": "branch", "relationshipName": "bank", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "branch", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "branch", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}], "service": "serviceImpl"}