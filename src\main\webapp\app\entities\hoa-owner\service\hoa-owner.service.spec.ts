import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';

import { IHoaOwner } from '../hoa-owner.model';
import { sampleWithRequiredData, sampleWithNewData, sampleWithPartialData, sampleWithFullData } from '../hoa-owner.test-samples';

import { HoaOwnerService, RestHoaOwner } from './hoa-owner.service';

const requireRestSample: RestHoaOwner = {
  ...sampleWithRequiredData,
  idExpiaryDate: sampleWithRequiredData.idExpiaryDate?.toJSON(),
};

describe('HoaOwner Service', () => {
  let service: HoaOwnerService;
  let httpMock: HttpTestingController;
  let expectedResult: IHoaOwner | IHoaOwner[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
    });
    expectedResult = null;
    service = TestBed.inject(HoaOwnerService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find(123).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a HoaOwner', () => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const hoaOwner = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(hoaOwner).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a HoaOwner', () => {
      const hoaOwner = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(hoaOwner).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a HoaOwner', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of HoaOwner', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a HoaOwner', () => {
      const expected = true;

      service.delete(123).subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    describe('addHoaOwnerToCollectionIfMissing', () => {
      it('should add a HoaOwner to an empty array', () => {
        const hoaOwner: IHoaOwner = sampleWithRequiredData;
        expectedResult = service.addHoaOwnerToCollectionIfMissing([], hoaOwner);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(hoaOwner);
      });

      it('should not add a HoaOwner to an array that contains it', () => {
        const hoaOwner: IHoaOwner = sampleWithRequiredData;
        const hoaOwnerCollection: IHoaOwner[] = [
          {
            ...hoaOwner,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addHoaOwnerToCollectionIfMissing(hoaOwnerCollection, hoaOwner);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a HoaOwner to an array that doesn't contain it", () => {
        const hoaOwner: IHoaOwner = sampleWithRequiredData;
        const hoaOwnerCollection: IHoaOwner[] = [sampleWithPartialData];
        expectedResult = service.addHoaOwnerToCollectionIfMissing(hoaOwnerCollection, hoaOwner);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(hoaOwner);
      });

      it('should add only unique HoaOwner to an array', () => {
        const hoaOwnerArray: IHoaOwner[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const hoaOwnerCollection: IHoaOwner[] = [sampleWithRequiredData];
        expectedResult = service.addHoaOwnerToCollectionIfMissing(hoaOwnerCollection, ...hoaOwnerArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const hoaOwner: IHoaOwner = sampleWithRequiredData;
        const hoaOwner2: IHoaOwner = sampleWithPartialData;
        expectedResult = service.addHoaOwnerToCollectionIfMissing([], hoaOwner, hoaOwner2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(hoaOwner);
        expect(expectedResult).toContain(hoaOwner2);
      });

      it('should accept null and undefined values', () => {
        const hoaOwner: IHoaOwner = sampleWithRequiredData;
        expectedResult = service.addHoaOwnerToCollectionIfMissing([], null, hoaOwner, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(hoaOwner);
      });

      it('should return initial array if no HoaOwner is added', () => {
        const hoaOwnerCollection: IHoaOwner[] = [sampleWithRequiredData];
        expectedResult = service.addHoaOwnerToCollectionIfMissing(hoaOwnerCollection, undefined, null);
        expect(expectedResult).toEqual(hoaOwnerCollection);
      });
    });

    describe('compareHoaOwner', () => {
      it('Should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareHoaOwner(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('Should return false if one entity is null', () => {
        const entity1 = { id: 123 };
        const entity2 = null;

        const compareResult1 = service.compareHoaOwner(entity1, entity2);
        const compareResult2 = service.compareHoaOwner(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey differs', () => {
        const entity1 = { id: 123 };
        const entity2 = { id: 456 };

        const compareResult1 = service.compareHoaOwner(entity1, entity2);
        const compareResult2 = service.compareHoaOwner(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey matches', () => {
        const entity1 = { id: 123 };
        const entity2 = { id: 123 };

        const compareResult1 = service.compareHoaOwner(entity1, entity2);
        const compareResult2 = service.compareHoaOwner(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
