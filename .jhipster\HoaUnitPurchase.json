{"applications": ["Escrow"], "changelogDate": "20230129131848", "dto": "mapstruct", "embedded": false, "entityTableName": "hoa_unit_purchase", "fields": [{"fieldName": "purchaseDate", "fieldType": "ZonedDateTime"}, {"fieldName": "saleRate", "fieldType": "Double"}, {"fieldName": "purchasePrice", "fieldType": "Double"}, {"fieldName": "unitRegistrationFee", "fieldType": "Double"}, {"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "agentId", "fieldType": "String"}, {"fieldName": "grossSaleprice", "fieldType": "Double"}, {"fieldName": "vatApplicable", "fieldType": "Boolean"}, {"fieldName": "deedNo", "fieldType": "String"}, {"fieldName": "agreementNo", "fieldType": "String"}, {"fieldName": "agreementDate", "fieldType": "ZonedDateTime"}, {"fieldName": "salePurchaseAgreement", "fieldType": "Boolean"}, {"fieldName": "worldCheck", "fieldType": "Boolean"}, {"fieldName": "amtPaidToDevInEscorw", "fieldType": "Double"}, {"fieldName": "amtPaidToDevOutEscorw", "fieldType": "Double"}, {"fieldName": "totalAmountPaid", "fieldType": "Double"}, {"fieldName": "unitIban", "fieldType": "String"}, {"fieldName": "oqood", "fieldType": "Boolean"}, {"fieldName": "oqoodPaid", "fieldType": "Boolean"}, {"fieldName": "oqoodAmountPaid", "fieldType": "String"}, {"fieldName": "unitAreaSize", "fieldType": "String"}, {"fieldName": "forfeitAmount", "fieldType": "String"}, {"fieldName": "dldAmount", "fieldType": "String"}, {"fieldName": "refundAmount", "fieldType": "String"}, {"fieldName": "remarks", "fieldType": "String"}, {"fieldName": "transferredAmount", "fieldType": "String"}, {"fieldName": "unitNoOtherFormat", "fieldType": "String"}, {"fieldName": "salePrice", "fieldType": "Double"}, {"fieldName": "projectPaymentPlan", "fieldType": "Boolean"}, {"fieldName": "reservationBookingForm", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "HoaUnitPurchase", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "hoaUnit", "otherEntityRelationshipName": "hoaUnitPurchase", "relationshipName": "hoaUnit", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "hoaUnitPurchase", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}