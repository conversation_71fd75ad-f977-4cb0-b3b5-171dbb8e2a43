{"applications": ["Escrow"], "changelogDate": "20220330114924", "dto": "mapstruct", "embedded": false, "entityTableName": "bucket_rule", "fields": [{"fieldName": "percentageToTranfer", "fieldType": "Integer"}, {"fieldName": "narration", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "BucketRule", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "bucketRule", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "bucketType", "relationshipType": "many-to-one"}, {"otherEntityName": "masterBankAccountType", "otherEntityRelationshipName": "bucketRule", "relationshipName": "masterBankAccountType", "relationshipType": "many-to-one"}, {"otherEntityName": "childBankAccountType", "otherEntityRelationshipName": "bucketRule", "relationshipName": "childBankAccountType", "relationshipType": "many-to-one"}], "service": "serviceImpl"}