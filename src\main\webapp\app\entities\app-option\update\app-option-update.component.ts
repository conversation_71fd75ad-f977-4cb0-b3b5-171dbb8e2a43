import { Component, OnInit } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import { AppOptionFormService, AppOptionFormGroup } from './app-option-form.service';
import { IAppOption } from '../app-option.model';
import { AppOptionService } from '../service/app-option.service';
import { AlertError } from 'app/shared/alert/alert-error.model';
import { EventManager, EventWithContent } from 'app/core/util/event-manager.service';
import { DataUtils, FileLoadError } from 'app/core/util/data-util.service';

@Component({
  selector: 'ab-app-option-update',
  templateUrl: './app-option-update.component.html',
})
export class AppOptionUpdateComponent implements OnInit {
  isSaving = false;
  appOption: IAppOption | null = null;

  appOptionsSharedCollection: IAppOption[] = [];

  editForm: AppOptionFormGroup = this.appOptionFormService.createAppOptionFormGroup();

  constructor(
    protected dataUtils: DataUtils,
    protected eventManager: EventManager,
    protected appOptionService: AppOptionService,
    protected appOptionFormService: AppOptionFormService,
    protected activatedRoute: ActivatedRoute
  ) {}

  compareAppOption = (o1: IAppOption | null, o2: IAppOption | null): boolean => this.appOptionService.compareAppOption(o1, o2);

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ appOption }) => {
      this.appOption = appOption;
      if (appOption) {
        this.updateForm(appOption);
      }

      this.loadRelationshipsOptions();
    });
  }

  byteSize(base64String: string): string {
    return this.dataUtils.byteSize(base64String);
  }

  openFile(base64String: string, contentType: string | null | undefined): void {
    this.dataUtils.openFile(base64String, contentType);
  }

  setFileData(event: Event, field: string, isImage: boolean): void {
    this.dataUtils.loadFileToForm(event, this.editForm, field, isImage).subscribe({
      error: (err: FileLoadError) =>
        this.eventManager.broadcast(new EventWithContent<AlertError>('escrowApp.error', { message: err.message })),
    });
  }

  previousState(): void {
    window.history.back();
  }

  save(): void {
    this.isSaving = true;
    const appOption = this.appOptionFormService.getAppOption(this.editForm);
    if (appOption.id !== null) {
      this.subscribeToSaveResponse(this.appOptionService.update(appOption));
    } else {
      this.subscribeToSaveResponse(this.appOptionService.create(appOption));
    }
  }

  protected subscribeToSaveResponse(result: Observable<HttpResponse<IAppOption>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: () => this.onSaveSuccess(),
      error: () => this.onSaveError(),
    });
  }

  protected onSaveSuccess(): void {
    this.previousState();
  }

  protected onSaveError(): void {
    // Api for inheritance.
  }

  protected onSaveFinalize(): void {
    this.isSaving = false;
  }

  protected updateForm(appOption: IAppOption): void {
    this.appOption = appOption;
    this.appOptionFormService.resetForm(this.editForm, appOption);

    this.appOptionsSharedCollection = this.appOptionService.addAppOptionToCollectionIfMissing<IAppOption>(
      this.appOptionsSharedCollection,
      ...(appOption.paymentFeeFrequencies ?? [])
    );
  }

  protected loadRelationshipsOptions(): void {
    this.appOptionService
      .query()
      .pipe(map((res: HttpResponse<IAppOption[]>) => res.body ?? []))
      .pipe(
        map((appOptions: IAppOption[]) =>
          this.appOptionService.addAppOptionToCollectionIfMissing<IAppOption>(appOptions, ...(this.appOption?.paymentFeeFrequencies ?? []))
        )
      )
      .subscribe((appOptions: IAppOption[]) => (this.appOptionsSharedCollection = appOptions));
  }
}
