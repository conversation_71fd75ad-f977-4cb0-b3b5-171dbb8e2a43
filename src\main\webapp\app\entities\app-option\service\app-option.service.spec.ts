import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';

import { IAppOption } from '../app-option.model';
import { sampleWithRequiredData, sampleWithNewData, sampleWithPartialData, sampleWithFullData } from '../app-option.test-samples';

import { AppOptionService } from './app-option.service';

const requireRestSample: IAppOption = {
  ...sampleWithRequiredData,
};

describe('AppOption Service', () => {
  let service: AppOptionService;
  let httpMock: HttpTestingController;
  let expectedResult: IAppOption | IAppOption[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
    });
    expectedResult = null;
    service = TestBed.inject(AppOptionService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find(123).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a AppOption', () => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const appOption = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(appOption).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a AppOption', () => {
      const appOption = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(appOption).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a AppOption', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of AppOption', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a AppOption', () => {
      const expected = true;

      service.delete(123).subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    describe('addAppOptionToCollectionIfMissing', () => {
      it('should add a AppOption to an empty array', () => {
        const appOption: IAppOption = sampleWithRequiredData;
        expectedResult = service.addAppOptionToCollectionIfMissing([], appOption);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(appOption);
      });

      it('should not add a AppOption to an array that contains it', () => {
        const appOption: IAppOption = sampleWithRequiredData;
        const appOptionCollection: IAppOption[] = [
          {
            ...appOption,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addAppOptionToCollectionIfMissing(appOptionCollection, appOption);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a AppOption to an array that doesn't contain it", () => {
        const appOption: IAppOption = sampleWithRequiredData;
        const appOptionCollection: IAppOption[] = [sampleWithPartialData];
        expectedResult = service.addAppOptionToCollectionIfMissing(appOptionCollection, appOption);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(appOption);
      });

      it('should add only unique AppOption to an array', () => {
        const appOptionArray: IAppOption[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const appOptionCollection: IAppOption[] = [sampleWithRequiredData];
        expectedResult = service.addAppOptionToCollectionIfMissing(appOptionCollection, ...appOptionArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const appOption: IAppOption = sampleWithRequiredData;
        const appOption2: IAppOption = sampleWithPartialData;
        expectedResult = service.addAppOptionToCollectionIfMissing([], appOption, appOption2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(appOption);
        expect(expectedResult).toContain(appOption2);
      });

      it('should accept null and undefined values', () => {
        const appOption: IAppOption = sampleWithRequiredData;
        expectedResult = service.addAppOptionToCollectionIfMissing([], null, appOption, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(appOption);
      });

      it('should return initial array if no AppOption is added', () => {
        const appOptionCollection: IAppOption[] = [sampleWithRequiredData];
        expectedResult = service.addAppOptionToCollectionIfMissing(appOptionCollection, undefined, null);
        expect(expectedResult).toEqual(appOptionCollection);
      });
    });

    describe('compareAppOption', () => {
      it('Should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareAppOption(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('Should return false if one entity is null', () => {
        const entity1 = { id: 123 };
        const entity2 = null;

        const compareResult1 = service.compareAppOption(entity1, entity2);
        const compareResult2 = service.compareAppOption(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey differs', () => {
        const entity1 = { id: 123 };
        const entity2 = { id: 456 };

        const compareResult1 = service.compareAppOption(entity1, entity2);
        const compareResult2 = service.compareAppOption(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey matches', () => {
        const entity1 = { id: 123 };
        const entity2 = { id: 123 };

        const compareResult1 = service.compareAppOption(entity1, entity2);
        const compareResult2 = service.compareAppOption(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
