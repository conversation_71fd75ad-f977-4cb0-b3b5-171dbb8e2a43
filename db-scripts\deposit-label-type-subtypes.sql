SELECT MAX(ID) FROM app_label;

INSERT INTO app_label (locale_id, id, ui_id, [value]) 
VALUES
    (1, 21060, 'BUYER_PAYMENT',	'Buyer Payment'),
    (1, 21061, 'DOWN_PAYMENT',	'Down Payment'),
    (1, 21062, 'FUND_TRANSFER',	'Fund Transfer'),
    (1, 21063, 'MORTGAGE_PAYMENT',	'Mortgage Payment'),
    (1, 21064, 'INTEREST/PROFIT_PAYMENT',	'Interest / Profit Payment'),
    (1, 21065, 'PARTNER_EQUITY_FUND',	'Partner Equity Fund'),
    (1, 21066, 'DEVELOPER_EQUITY_PAYMENT',	'Developer Equity Payment'),
    (1, 21067, 'FORFEITED_PAYMENT',	'Forfeited Payment'),
    (1, 21068, 'RETENTION_TRANSFER',	'Retention Transfer'),
    (1, 21069, 'PERFORMANCE_BONDS_LIQUIDATION',	'Performance Bonds Liquidation'),
    (1, 21070, 'VAT_PAYMENT_REQUEST',	'VAT Payment Request'),
    (1, 21071, 'UNSPECIFIED_BUYER_PAYMENT',	'Unspecified Buyer Payment'),
    (1, 21072, 'DLD_FEES',	'DLD Fees'),
    (1, 21074, 'PREMIUM_INSTALLMENTS',	'Premium Installments'),
    (1, 21073, 'UNIT_REGISTRATION',	'Unit Registration'),
    (1, 21075, 'CASH',	'Cash'),
    (1, 21076, 'WIRE_TRANSFER',	'Wire Transfer'),
    (1, 21077, 'CREDIT_CARD',	'Credit Card'),
    (1, 21078, 'INTERNAL_TRANSFER',	'Internal Transfer'),
	(1, 21079, 'PREMIUM',	'Premium'),
    (1, 21080, 'INSTALLMENTS',	'Installments');


SELECT TOP (1000) [id]
      ,[description]
      ,[label_id]
      ,[option_key]
      ,[option_value]
      ,[option_label]
  FROM [ESC_NBF_RERA_API_SIT].[dbo].[app_option]
  WHERE option_key IN ('DEPOSIT_MODE')

SELECT MAX(id) FROM app_option 

UPDATE app_option SET [label_id]='WIRE_TRANSFER' WHERE option_value='DM_ACCOUNT_TRANSFER';
UPDATE app_option SET [label_id]='CREDIT_CARD' WHERE option_value='DM_CARD';
UPDATE app_option SET [label_id]='CASH' WHERE option_value='DM_CASH_DEPOSIT';
UPDATE app_option SET [label_id]='INTERNAL_TRANSFER' WHERE option_value='DM_INWARD_REMITTANCE';
INSERT INTO app_option (id, [label_id], option_key, option_value) VALUES (14301 , 'UNIT_REGISTRATION', 'DEPOSIT_MODE', 'DM_UNIT_REGISTRATION');




UPDATE app_option SET [label_id]='BUYER_PAYMENT' WHERE option_value='BT_UNIT_HOLDER_DEPOSIT';
UPDATE app_option SET [label_id]='INTEREST/PROFIT_PAYMENT' WHERE option_value='BT_CREDIT_INT/PROFIT';
UPDATE app_option SET [label_id]='VAT_PAYMENT_REQUEST' WHERE option_value='BT_VAT';
UPDATE app_option SET [label_id]='UNSPECIFIED_BUYER_PAYMENT' WHERE option_value='BT_UNALLOCATED';

UPDATE app_option SET [label_id]='FUND_TRANSFER', option_value='BT_FUND_TRANSFER' WHERE option_value='BT_DLD';
UPDATE app_option SET [label_id]='MORTGAGE_PAYMENT', option_value='BT_MORTGAGE_PAYMENT' WHERE option_value='BT_OTHERS';
UPDATE app_option SET [label_id]='PARTNER_EQUITY_FUND', option_value='BT_PARTNER_EQUITY_FUND' WHERE option_value='BT_ERRORNESS_PAYMENT';
UPDATE app_option SET [label_id]='DEVELOPER_EQUITY_PAYMENT', option_value='BT_DEVELOPER_EQUITY_PAYMENT' WHERE option_value='BT_DEVELOPER_FUNDS';

INSERT INTO app_option (id, [label_id], option_key, option_value) 
VALUES 
(14302, 'FORFEITED_PAYMENT', 'BUCKET_TYPE','BT_FORFEITED_PAYMENT'),
(14303, 'RETENTION_TRANSFER', 'BUCKET_TYPE','BT_RETENTION_TRANSFER'),
(14304, 'PERFORMANCE_BONDS_LIQUIDATION', 'BUCKET_TYPE','BT_PERFORMANCE_BONDS_LIQUIDATION');



UPDATE app_option SET [label_id]='DOWN_PAYMENT', option_value='BTSC_DOWN_PAYMENT', [description]='{"types": ["BT_UNIT_HOLDER_DEPOSIT", "BT_FORFEITED_PAYMENT", "BT_UNALLOCATED"]}' WHERE option_value='T_DEVELOPER' AND option_key='BUCKET_TYPE_SUB_CAT';
UPDATE app_option SET [label_id]='UNIT_REGISTRATION', option_value='BTSC_UNIT_REGISTRATION', [description]='{"types": ["BT_UNIT_HOLDER_DEPOSIT", "BT_FORFEITED_PAYMENT", "BT_UNALLOCATED"]}' WHERE option_value='T_UNIT_HOLDER' AND option_key='BUCKET_TYPE_SUB_CAT';
UPDATE app_option SET [label_id]='PREMIUM', option_value='BTSC_PREMIUM', [description]='{"types": ["BT_UNIT_HOLDER_DEPOSIT", "BT_FORFEITED_PAYMENT", "BT_UNALLOCATED"]}' WHERE option_value='T_TRANSFER_FROM_DEVELOPER' AND option_key='BUCKET_TYPE_SUB_CAT';
UPDATE app_option SET [label_id]='INSTALLMENTS', option_value='BTSC_INSTALLMENTS', [description]='{"types": ["BT_UNIT_HOLDER_DEPOSIT", "BT_FORFEITED_PAYMENT", "BT_UNALLOCATED"]}' WHERE option_value='T_LOAN/MORTGAGE' AND option_key='BUCKET_TYPE_SUB_CAT';

