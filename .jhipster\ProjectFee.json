{"applications": ["Escrow"], "changelogDate": "20220330112924", "dto": "mapstruct", "embedded": false, "entityTableName": "project_fee", "fields": [{"fieldName": "amount", "fieldType": "Double"}, {"fieldName": "totalAmount", "fieldType": "Double"}, {"fieldName": "feeCalender", "fieldType": "ZonedDateTime"}, {"fieldName": "feeNextRecoveryDate", "fieldType": "ZonedDateTime"}, {"fieldName": "vatPercentage", "fieldType": "Double"}, {"fieldName": "feeCollected", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "ProjectFee", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "project", "otherEntityRelationshipName": "projectFee", "relationshipName": "project", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "projectFeeCategory", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "projectFeeCurrency", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "projectPaymentFrequency", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "relationshipName": "debitAccount", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "projectFee", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "projectFeeHistory", "otherEntityRelationshipName": "projectFee", "relationshipName": "projectFeeHistory", "relationshipType": "one-to-many"}], "service": "serviceImpl"}