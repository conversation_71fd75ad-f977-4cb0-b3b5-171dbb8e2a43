import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { UserRouteAccessService } from 'app/core/auth/user-route-access.service';
import { HoaUnitComponent } from '../list/hoa-unit.component';
import { HoaUnitDetailComponent } from '../detail/hoa-unit-detail.component';
import { HoaUnitUpdateComponent } from '../update/hoa-unit-update.component';
import { HoaUnitRoutingResolveService } from './hoa-unit-routing-resolve.service';
import { ASC } from 'app/config/navigation.constants';

const hoaUnitRoute: Routes = [
  {
    path: '',
    component: HoaUnitComponent,
    data: {
      defaultSort: 'id,' + ASC,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':id/view',
    component: HoaUnitDetailComponent,
    resolve: {
      hoaUnit: HoaUnitRoutingResolveService,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: 'new',
    component: HoaUnitUpdateComponent,
    resolve: {
      hoaUnit: HoaUnitRoutingResolveService,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':id/edit',
    component: HoaUnitUpdateComponent,
    resolve: {
      hoaUnit: HoaUnitRoutingResolveService,
    },
    canActivate: [UserRouteAccessService],
  },
];

@NgModule({
  imports: [RouterModule.forChild(hoaUnitRoute)],
  exports: [RouterModule],
})
export class HoaUnitRoutingModule {}
