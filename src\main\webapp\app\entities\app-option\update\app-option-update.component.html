<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" role="form" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2 id="ab-app-option-heading" data-cy="AppOptionCreateUpdateHeading">Create or edit a App Option</h2>

      <div>
        <ab-alert-error></ab-alert-error>

        <div class="row mb-3" *ngIf="editForm.controls.id.value !== null">
          <label class="form-label" for="field_id">ID</label>
          <input type="number" class="form-control" name="id" id="field_id" data-cy="id" formControlName="id" [readonly]="true" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_optionKey">Option Key</label>
          <input type="text" class="form-control" name="optionKey" id="field_optionKey" data-cy="optionKey" formControlName="optionKey" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_optionValue">Option Value</label>
          <input
            type="text"
            class="form-control"
            name="optionValue"
            id="field_optionValue"
            data-cy="optionValue"
            formControlName="optionValue"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_labelId">Label Id</label>
          <input type="text" class="form-control" name="labelId" id="field_labelId" data-cy="labelId" formControlName="labelId" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_description">Description</label>
          <textarea
            class="form-control"
            name="description"
            id="field_description"
            data-cy="description"
            formControlName="description"
          ></textarea>
        </div>

        <div class="row mb-3">
          <label for="field_paymentFeeFrequencies">Payment Fee Frequency</label>
          <select
            class="form-control"
            id="field_paymentFeeFrequencies"
            data-cy="paymentFeeFrequency"
            multiple
            name="paymentFeeFrequencies"
            formControlName="paymentFeeFrequencies"
            [compareWith]="compareAppOption"
          >
            <option [ngValue]="appOptionOption" *ngFor="let appOptionOption of appOptionsSharedCollection">{{ appOptionOption.id }}</option>
          </select>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span>Cancel</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span>Save</span>
        </button>
      </div>
    </form>
  </div>
</div>
