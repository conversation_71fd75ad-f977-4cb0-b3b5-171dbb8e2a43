{"applications": ["Escrow"], "changelogDate": "20230129131846", "dto": "mapstruct", "embedded": false, "entityTableName": "hoa_unit", "fields": [{"fieldName": "unitNo", "fieldType": "String"}, {"fieldName": "unitRefId", "fieldType": "String"}, {"fieldName": "altUnitRefId", "fieldType": "String"}, {"fieldName": "name", "fieldType": "String"}, {"fieldName": "isResale", "fieldType": "Boolean"}, {"fieldName": "resaleDate", "fieldType": "ZonedDateTime"}, {"fieldName": "unitSysId", "fieldType": "String"}, {"fieldName": "otherFormatUnitNo", "fieldType": "String"}, {"fieldName": "virtualAccNo", "fieldType": "String"}, {"fieldName": "towerName", "fieldType": "String"}, {"fieldName": "unitPlotSize", "fieldType": "String"}, {"fieldName": "floor", "fieldType": "String"}, {"fieldName": "noofBedroom", "fieldType": "String"}, {"fieldName": "unitIban", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "HoaUnit", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "hoaUnit", "otherEntityRelationshipName": "ho<PERSON><PERSON><PERSON><PERSON>", "relationshipName": "hoa<PERSON>arent", "relationshipType": "many-to-one"}, {"otherEntityName": "hoaUnitType", "otherEntityRelationshipName": "hoaUnit", "relationshipName": "hoaUnitType", "relationshipType": "many-to-one"}, {"otherEntityName": "property", "otherEntityRelationshipName": "hoaUnit", "relationshipName": "property", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "hoaUnitStatus", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "propertyId", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "hoaCreditCurrency", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "hoaPrchPriceCur", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "hoaUnitPmtPlanType", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "hoaUnit", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "hoaUnit", "otherEntityRelationshipName": "hoa<PERSON>arent", "relationshipName": "ho<PERSON><PERSON><PERSON><PERSON>", "relationshipType": "one-to-many"}, {"otherEntityName": "hoaUnitPurchase", "otherEntityRelationshipName": "hoaUnit", "relationshipName": "hoaUnitPurchase", "relationshipType": "one-to-many"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "hoaUnit", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}, {"otherEntityName": "hoa<PERSON>wner", "otherEntityRelationshipName": "hoaUnit", "relationshipName": "hoa<PERSON>wner", "relationshipType": "one-to-many"}], "service": "serviceImpl"}