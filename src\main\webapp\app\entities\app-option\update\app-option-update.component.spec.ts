import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { of, Subject, from } from 'rxjs';

import { AppOptionFormService } from './app-option-form.service';
import { AppOptionService } from '../service/app-option.service';
import { IAppOption } from '../app-option.model';

import { AppOptionUpdateComponent } from './app-option-update.component';

describe('AppOption Management Update Component', () => {
  let comp: AppOptionUpdateComponent;
  let fixture: ComponentFixture<AppOptionUpdateComponent>;
  let activatedRoute: ActivatedRoute;
  let appOptionFormService: AppOptionFormService;
  let appOptionService: AppOptionService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterTestingModule.withRoutes([])],
      declarations: [AppOptionUpdateComponent],
      providers: [
        FormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            params: from([{}]),
          },
        },
      ],
    })
      .overrideTemplate(AppOptionUpdateComponent, '')
      .compileComponents();

    fixture = TestBed.createComponent(AppOptionUpdateComponent);
    activatedRoute = TestBed.inject(ActivatedRoute);
    appOptionFormService = TestBed.inject(AppOptionFormService);
    appOptionService = TestBed.inject(AppOptionService);

    comp = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('Should call AppOption query and add missing value', () => {
      const appOption: IAppOption = { id: 456 };
      const paymentFeeFrequencies: IAppOption[] = [{ id: 35989 }];
      appOption.paymentFeeFrequencies = paymentFeeFrequencies;

      const appOptionCollection: IAppOption[] = [{ id: 38175 }];
      jest.spyOn(appOptionService, 'query').mockReturnValue(of(new HttpResponse({ body: appOptionCollection })));
      const additionalAppOptions = [...paymentFeeFrequencies];
      const expectedCollection: IAppOption[] = [...additionalAppOptions, ...appOptionCollection];
      jest.spyOn(appOptionService, 'addAppOptionToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ appOption });
      comp.ngOnInit();

      expect(appOptionService.query).toHaveBeenCalled();
      expect(appOptionService.addAppOptionToCollectionIfMissing).toHaveBeenCalledWith(
        appOptionCollection,
        ...additionalAppOptions.map(expect.objectContaining)
      );
      expect(comp.appOptionsSharedCollection).toEqual(expectedCollection);
    });

    it('Should update editForm', () => {
      const appOption: IAppOption = { id: 456 };
      const paymentFeeFrequency: IAppOption = { id: 51869 };
      appOption.paymentFeeFrequencies = [paymentFeeFrequency];

      activatedRoute.data = of({ appOption });
      comp.ngOnInit();

      expect(comp.appOptionsSharedCollection).toContain(paymentFeeFrequency);
      expect(comp.appOption).toEqual(appOption);
    });
  });

  describe('save', () => {
    it('Should call update service on save for existing entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAppOption>>();
      const appOption = { id: 123 };
      jest.spyOn(appOptionFormService, 'getAppOption').mockReturnValue(appOption);
      jest.spyOn(appOptionService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ appOption });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: appOption }));
      saveSubject.complete();

      // THEN
      expect(appOptionFormService.getAppOption).toHaveBeenCalled();
      expect(comp.previousState).toHaveBeenCalled();
      expect(appOptionService.update).toHaveBeenCalledWith(expect.objectContaining(appOption));
      expect(comp.isSaving).toEqual(false);
    });

    it('Should call create service on save for new entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAppOption>>();
      const appOption = { id: 123 };
      jest.spyOn(appOptionFormService, 'getAppOption').mockReturnValue({ id: null });
      jest.spyOn(appOptionService, 'create').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ appOption: null });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: appOption }));
      saveSubject.complete();

      // THEN
      expect(appOptionFormService.getAppOption).toHaveBeenCalled();
      expect(appOptionService.create).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).toHaveBeenCalled();
    });

    it('Should set isSaving to false on error', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAppOption>>();
      const appOption = { id: 123 };
      jest.spyOn(appOptionService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ appOption });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.error('This is an error!');

      // THEN
      expect(appOptionService.update).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).not.toHaveBeenCalled();
    });
  });

  describe('Compare relationships', () => {
    describe('compareAppOption', () => {
      it('Should forward to appOptionService', () => {
        const entity = { id: 123 };
        const entity2 = { id: 456 };
        jest.spyOn(appOptionService, 'compareAppOption');
        comp.compareAppOption(entity, entity2);
        expect(appOptionService.compareAppOption).toHaveBeenCalledWith(entity, entity2);
      });
    });
  });
});
