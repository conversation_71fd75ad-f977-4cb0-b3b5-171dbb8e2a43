<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" role="form" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2 id="ab-hoa-owner-heading" data-cy="HoaOwnerCreateUpdateHeading">Create or edit a Hoa Owner</h2>

      <div>
        <ab-alert-error></ab-alert-error>

        <div class="row mb-3" *ngIf="editForm.controls.id.value !== null">
          <label class="form-label" for="field_id">ID</label>
          <input type="number" class="form-control" name="id" id="field_id" data-cy="id" formControlName="id" [readonly]="true" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_ownerId">Owner Id</label>
          <input type="text" class="form-control" name="ownerId" id="field_ownerId" data-cy="ownerId" formControlName="ownerId" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_name">Name</label>
          <input type="text" class="form-control" name="name" id="field_name" data-cy="name" formControlName="name" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_middleName">Middle Name</label>
          <input
            type="text"
            class="form-control"
            name="middleName"
            id="field_middleName"
            data-cy="middleName"
            formControlName="middleName"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_lastName">Last Name</label>
          <input type="text" class="form-control" name="lastName" id="field_lastName" data-cy="lastName" formControlName="lastName" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_ownershipPercentage">Ownership Percentage</label>
          <input
            type="number"
            class="form-control"
            name="ownershipPercentage"
            id="field_ownershipPercentage"
            data-cy="ownershipPercentage"
            formControlName="ownershipPercentage"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_idNo">Id No</label>
          <input type="text" class="form-control" name="idNo" id="field_idNo" data-cy="idNo" formControlName="idNo" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_contactTel">Contact Tel</label>
          <input
            type="text"
            class="form-control"
            name="contactTel"
            id="field_contactTel"
            data-cy="contactTel"
            formControlName="contactTel"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_mobile">Mobile</label>
          <input type="text" class="form-control" name="mobile" id="field_mobile" data-cy="mobile" formControlName="mobile" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_email">Email</label>
          <input type="text" class="form-control" name="email" id="field_email" data-cy="email" formControlName="email" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_ownerNumber">Owner Number</label>
          <input
            type="number"
            class="form-control"
            name="ownerNumber"
            id="field_ownerNumber"
            data-cy="ownerNumber"
            formControlName="ownerNumber"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_isCurrent">Is Current</label>
          <input type="checkbox" class="form-check" name="isCurrent" id="field_isCurrent" data-cy="isCurrent" formControlName="isCurrent" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_idExpiaryDate">Id Expiary Date</label>
          <div class="d-flex">
            <input
              id="field_idExpiaryDate"
              data-cy="idExpiaryDate"
              type="datetime-local"
              class="form-control"
              name="idExpiaryDate"
              formControlName="idExpiaryDate"
              placeholder="YYYY-MM-DD HH:mm"
            />
          </div>
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_localeName">Locale Name</label>
          <input
            type="text"
            class="form-control"
            name="localeName"
            id="field_localeName"
            data-cy="localeName"
            formControlName="localeName"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_reservepercentage">Reservepercentage</label>
          <input
            type="number"
            class="form-control"
            name="reservepercentage"
            id="field_reservepercentage"
            data-cy="reservepercentage"
            formControlName="reservepercentage"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_unitMollacId">Unit Mollac Id</label>
          <input
            type="text"
            class="form-control"
            name="unitMollacId"
            id="field_unitMollacId"
            data-cy="unitMollacId"
            formControlName="unitMollacId"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_noofBedroom">Noof Bedroom</label>
          <input
            type="text"
            class="form-control"
            name="noofBedroom"
            id="field_noofBedroom"
            data-cy="noofBedroom"
            formControlName="noofBedroom"
          />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_floor">Floor</label>
          <input type="text" class="form-control" name="floor" id="field_floor" data-cy="floor" formControlName="floor" />
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_hoaCountryOption">Hoa Country Option</label>
          <select
            class="form-control"
            id="field_hoaCountryOption"
            data-cy="hoaCountryOption"
            name="hoaCountryOption"
            formControlName="hoaCountryOption"
            [compareWith]="compareAppOption"
          >
            <option [ngValue]="null"></option>
            <option [ngValue]="appOptionOption" *ngFor="let appOptionOption of appOptionsSharedCollection">{{ appOptionOption.id }}</option>
          </select>
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_hoaInvesterType">Hoa Invester Type</label>
          <select
            class="form-control"
            id="field_hoaInvesterType"
            data-cy="hoaInvesterType"
            name="hoaInvesterType"
            formControlName="hoaInvesterType"
            [compareWith]="compareAppOption"
          >
            <option [ngValue]="null"></option>
            <option [ngValue]="appOptionOption" *ngFor="let appOptionOption of appOptionsSharedCollection">{{ appOptionOption.id }}</option>
          </select>
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_hoaInvesterIdType">Hoa Invester Id Type</label>
          <select
            class="form-control"
            id="field_hoaInvesterIdType"
            data-cy="hoaInvesterIdType"
            name="hoaInvesterIdType"
            formControlName="hoaInvesterIdType"
            [compareWith]="compareAppOption"
          >
            <option [ngValue]="null"></option>
            <option [ngValue]="appOptionOption" *ngFor="let appOptionOption of appOptionsSharedCollection">{{ appOptionOption.id }}</option>
          </select>
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_hoaUnit">Hoa Unit</label>
          <select
            class="form-control"
            id="field_hoaUnit"
            data-cy="hoaUnit"
            name="hoaUnit"
            formControlName="hoaUnit"
            [compareWith]="compareHoaUnit"
          >
            <option [ngValue]="null"></option>
            <option [ngValue]="hoaUnitOption" *ngFor="let hoaUnitOption of hoaUnitsSharedCollection">{{ hoaUnitOption.id }}</option>
          </select>
        </div>

        <div class="row mb-3">
          <label class="form-label" for="field_workFlowStates">Work Flow States</label>
          <select
            class="form-control"
            id="field_workFlowStates"
            data-cy="workFlowStates"
            name="workFlowStates"
            formControlName="workFlowStates"
            [compareWith]="compareWorkFlowStates"
          >
            <option [ngValue]="null"></option>
            <option [ngValue]="workFlowStatesOption" *ngFor="let workFlowStatesOption of workFlowStatesSharedCollection">
              {{ workFlowStatesOption.id }}
            </option>
          </select>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span>Cancel</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span>Save</span>
        </button>
      </div>
    </form>
  </div>
</div>
