import { Injectable } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { IAppOption, NewAppOption } from '../app-option.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { id: unknown }> = Partial<Omit<T, 'id'>> & { id: T['id'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts IAppOption for edit and NewAppOptionFormGroupInput for create.
 */
type AppOptionFormGroupInput = IAppOption | PartialWithRequiredKeyOf<NewAppOption>;

type AppOptionFormDefaults = Pick<
  NewAppOption,
  'id' | 'paymentFeeFrequencies' | 'projectOptions' | 'projectBlockOptions' | 'projFeeCategories' | 'propertyBlockOptions'
>;

type AppOptionFormGroupContent = {
  id: FormControl<IAppOption['id'] | NewAppOption['id']>;
  optionKey: FormControl<IAppOption['optionKey']>;
  optionValue: FormControl<IAppOption['optionValue']>;
  labelId: FormControl<IAppOption['labelId']>;
  description: FormControl<IAppOption['description']>;
  paymentFeeFrequencies: FormControl<IAppOption['paymentFeeFrequencies']>;
  projectOptions: FormControl<IAppOption['projectOptions']>;
  projectBlockOptions: FormControl<IAppOption['projectBlockOptions']>;
  projFeeCategories: FormControl<IAppOption['projFeeCategories']>;
  propertyBlockOptions: FormControl<IAppOption['propertyBlockOptions']>;
};

export type AppOptionFormGroup = FormGroup<AppOptionFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class AppOptionFormService {
  createAppOptionFormGroup(appOption: AppOptionFormGroupInput = { id: null }): AppOptionFormGroup {
    const appOptionRawValue = {
      ...this.getFormDefaults(),
      ...appOption,
    };
    return new FormGroup<AppOptionFormGroupContent>({
      id: new FormControl(
        { value: appOptionRawValue.id, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        }
      ),
      optionKey: new FormControl(appOptionRawValue.optionKey),
      optionValue: new FormControl(appOptionRawValue.optionValue),
      labelId: new FormControl(appOptionRawValue.labelId),
      description: new FormControl(appOptionRawValue.description),
      paymentFeeFrequencies: new FormControl(appOptionRawValue.paymentFeeFrequencies ?? []),
      projectOptions: new FormControl(appOptionRawValue.projectOptions ?? []),
      projectBlockOptions: new FormControl(appOptionRawValue.projectBlockOptions ?? []),
      projFeeCategories: new FormControl(appOptionRawValue.projFeeCategories ?? []),
      propertyBlockOptions: new FormControl(appOptionRawValue.propertyBlockOptions ?? []),
    });
  }

  getAppOption(form: AppOptionFormGroup): IAppOption | NewAppOption {
    return form.getRawValue() as IAppOption | NewAppOption;
  }

  resetForm(form: AppOptionFormGroup, appOption: AppOptionFormGroupInput): void {
    const appOptionRawValue = { ...this.getFormDefaults(), ...appOption };
    form.reset(
      {
        ...appOptionRawValue,
        id: { value: appOptionRawValue.id, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */
    );
  }

  private getFormDefaults(): AppOptionFormDefaults {
    return {
      id: null,
      paymentFeeFrequencies: [],
      projectOptions: [],
      projectBlockOptions: [],
      projFeeCategories: [],
      propertyBlockOptions: [],
    };
  }
}
