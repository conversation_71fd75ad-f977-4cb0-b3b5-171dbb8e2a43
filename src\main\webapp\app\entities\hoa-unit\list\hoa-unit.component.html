<div>
  <h2 id="page-heading" data-cy="HoaUnitHeading">
    <span>Hoa Units</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [spin]="isLoading"></fa-icon>
        <span>Refresh list</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-hoa-unit"
        [routerLink]="['/hoa-unit/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span> Create a new Hoa Unit </span>
      </button>
    </div>
  </h2>

  <ab-alert-error></ab-alert-error>

  <ab-alert></ab-alert>

  <ab-filter [filters]="filters"></ab-filter>

  <div class="alert alert-warning" id="no-result" *ngIf="hoaUnits?.length === 0">
    <span>No Hoa Units found</span>
  </div>

  <div class="table-responsive table-entities" id="entities" *ngIf="hoaUnits && hoaUnits.length > 0">
    <table class="table table-striped" aria-describedby="page-heading">
      <thead>
        <tr abSort [(predicate)]="predicate" [(ascending)]="ascending" (sortChange)="navigateToWithComponentValues()">
          <th scope="col" abSortBy="id">
            <div class="d-flex">
              <span>ID</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="unitNo">
            <div class="d-flex">
              <span>Unit No</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="unitRefId">
            <div class="d-flex">
              <span>Unit Ref Id</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="altUnitRefId">
            <div class="d-flex">
              <span>Alt Unit Ref Id</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="name">
            <div class="d-flex">
              <span>Name</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="isResale">
            <div class="d-flex">
              <span>Is Resale</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="resaleDate">
            <div class="d-flex">
              <span>Resale Date</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="unitSysId">
            <div class="d-flex">
              <span>Unit Sys Id</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="otherFormatUnitNo">
            <div class="d-flex">
              <span>Other Format Unit No</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="virtualAccNo">
            <div class="d-flex">
              <span>Virtual Acc No</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="towerName">
            <div class="d-flex">
              <span>Tower Name</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="unitPlotSize">
            <div class="d-flex">
              <span>Unit Plot Size</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="floor">
            <div class="d-flex">
              <span>Floor</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="noofBedroom">
            <div class="d-flex">
              <span>Noof Bedroom</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="unitIban">
            <div class="d-flex">
              <span>Unit Iban</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="hoaParent.id">
            <div class="d-flex">
              <span>Hoa Parent</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="hoaUnitType.id">
            <div class="d-flex">
              <span>Hoa Unit Type</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="property.id">
            <div class="d-flex">
              <span>Property</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="hoaUnitStatus.id">
            <div class="d-flex">
              <span>Hoa Unit Status</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="propertyId.id">
            <div class="d-flex">
              <span>Property Id</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="hoaCreditCurrency.id">
            <div class="d-flex">
              <span>Hoa Credit Currency</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="hoaPrchPriceCur.id">
            <div class="d-flex">
              <span>Hoa Prch Price Cur</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="hoaUnitPmtPlanType.id">
            <div class="d-flex">
              <span>Hoa Unit Pmt Plan Type</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="workFlowStates.id">
            <div class="d-flex">
              <span>Work Flow States</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col"></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let hoaUnit of hoaUnits; trackBy: trackId" data-cy="entityTable">
          <td>
            <a [routerLink]="['/hoa-unit', hoaUnit.id, 'view']">{{ hoaUnit.id }}</a>
          </td>
          <td>{{ hoaUnit.unitNo }}</td>
          <td>{{ hoaUnit.unitRefId }}</td>
          <td>{{ hoaUnit.altUnitRefId }}</td>
          <td>{{ hoaUnit.name }}</td>
          <td>{{ hoaUnit.isResale }}</td>
          <td>{{ hoaUnit.resaleDate | formatMediumDatetime }}</td>
          <td>{{ hoaUnit.unitSysId }}</td>
          <td>{{ hoaUnit.otherFormatUnitNo }}</td>
          <td>{{ hoaUnit.virtualAccNo }}</td>
          <td>{{ hoaUnit.towerName }}</td>
          <td>{{ hoaUnit.unitPlotSize }}</td>
          <td>{{ hoaUnit.floor }}</td>
          <td>{{ hoaUnit.noofBedroom }}</td>
          <td>{{ hoaUnit.unitIban }}</td>
          <td>
            <div *ngIf="hoaUnit.hoaParent">
              <a [routerLink]="['/hoa-unit', hoaUnit.hoaParent.id, 'view']">{{ hoaUnit.hoaParent.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="hoaUnit.hoaUnitType">
              <a [routerLink]="['/hoa-unit-type', hoaUnit.hoaUnitType.id, 'view']">{{ hoaUnit.hoaUnitType.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="hoaUnit.property">
              <a [routerLink]="['/property', hoaUnit.property.id, 'view']">{{ hoaUnit.property.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="hoaUnit.hoaUnitStatus">
              <a [routerLink]="['/app-option', hoaUnit.hoaUnitStatus.id, 'view']">{{ hoaUnit.hoaUnitStatus.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="hoaUnit.propertyId">
              <a [routerLink]="['/app-option', hoaUnit.propertyId.id, 'view']">{{ hoaUnit.propertyId.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="hoaUnit.hoaCreditCurrency">
              <a [routerLink]="['/app-option', hoaUnit.hoaCreditCurrency.id, 'view']">{{ hoaUnit.hoaCreditCurrency.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="hoaUnit.hoaPrchPriceCur">
              <a [routerLink]="['/app-option', hoaUnit.hoaPrchPriceCur.id, 'view']">{{ hoaUnit.hoaPrchPriceCur.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="hoaUnit.hoaUnitPmtPlanType">
              <a [routerLink]="['/app-option', hoaUnit.hoaUnitPmtPlanType.id, 'view']">{{ hoaUnit.hoaUnitPmtPlanType.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="hoaUnit.workFlowStates">
              <a [routerLink]="['/work-flow-states', hoaUnit.workFlowStates.id, 'view']">{{ hoaUnit.workFlowStates.id }}</a>
            </div>
          </td>
          <td class="text-end">
            <div class="btn-group">
              <button
                type="submit"
                [routerLink]="['/hoa-unit']"
                [queryParams]="{ 'filter[hoaParentId.in]': hoaUnit.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Hoa Unit</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/hoa-unit-purchase']"
                [queryParams]="{ 'filter[hoaUnitId.in]': hoaUnit.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Hoa Unit Purchase</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/bank-account']"
                [queryParams]="{ 'filter[hoaUnitId.in]': hoaUnit.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Bank Account</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/hoa-owner']"
                [queryParams]="{ 'filter[hoaUnitId.in]': hoaUnit.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Hoa Owner</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/hoa-unit', hoaUnit.id, 'view']"
                class="btn btn-info btn-sm"
                data-cy="entityDetailsButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">View</span>
              </button>

              <button
                type="submit"
                [routerLink]="['/hoa-unit', hoaUnit.id, 'edit']"
                class="btn btn-primary btn-sm"
                data-cy="entityEditButton"
              >
                <fa-icon icon="pencil-alt"></fa-icon>
                <span class="d-none d-md-inline">Edit</span>
              </button>

              <button type="submit" (click)="delete(hoaUnit)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                <fa-icon icon="times"></fa-icon>
                <span class="d-none d-md-inline">Delete</span>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div *ngIf="hoaUnits && hoaUnits.length > 0">
    <div class="d-flex justify-content-center">
      <ab-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></ab-item-count>
    </div>

    <div class="d-flex justify-content-center">
      <ngb-pagination
        [collectionSize]="totalItems"
        [page]="page"
        [pageSize]="itemsPerPage"
        [maxSize]="5"
        [rotate]="true"
        [boundaryLinks]="true"
        (pageChange)="navigateToPage($event)"
      ></ngb-pagination>
    </div>
  </div>
</div>
