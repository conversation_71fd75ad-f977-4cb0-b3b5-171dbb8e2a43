{"applications": ["Escrow"], "changelogDate": "20220330111424", "dto": "mapstruct", "embedded": false, "entityTableName": "app_table_design", "fields": [{"fieldName": "name", "fieldType": "String"}, {"fieldName": "tableDefination", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "AppTableDesign", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceImpl"}