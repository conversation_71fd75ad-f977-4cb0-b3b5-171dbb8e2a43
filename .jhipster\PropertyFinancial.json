{"applications": ["Escrow"], "changelogDate": "20230129131845", "dto": "mapstruct", "embedded": false, "entityTableName": "property_financial", "fields": [{"fieldName": "propertyInitiatedDate", "fieldType": "ZonedDateTime"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "PropertyFinancial", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "propertyMgmtCompany", "otherEntityRelationshipName": "propertyFinancial", "relationshipName": "propertyMgmtCompany", "relationshipType": "many-to-one"}, {"otherEntityName": "property", "otherEntityRelationshipName": "propertyFinancial", "relationshipName": "property", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "propertyFinancial", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}