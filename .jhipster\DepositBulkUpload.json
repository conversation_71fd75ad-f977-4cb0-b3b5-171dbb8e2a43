{"applications": ["Escrow"], "changelogDate": "20250212082929", "dto": "mapstruct", "embedded": false, "entityTableName": "deposit_bulk_upload", "fields": [{"fieldName": "projectName", "fieldType": "String"}, {"fieldName": "totalCredit", "fieldType": "Double"}, {"fieldName": "totalDebit", "fieldType": "Double"}, {"fieldName": "endingBalance", "fieldType": "Double"}, {"fieldName": "paymentRef", "fieldType": "String"}, {"fieldName": "tasRefNumber", "fieldType": "String"}, {"fieldName": "credit", "fieldType": "Double"}, {"fieldName": "debit", "fieldType": "Double"}, {"fieldName": "vat", "fieldType": "Double"}, {"fieldName": "requestStatus", "fieldType": "String"}, {"fieldName": "requestDate", "fieldType": "ZonedDateTime"}, {"fieldName": "propertyDetails", "fieldType": "String"}, {"fieldName": "isCorporate", "fieldType": "String"}, {"fieldName": "isSettled", "fieldType": "String"}, {"fieldName": "corporatePayment", "fieldType": "Double"}, {"fieldName": "uploadingDate", "fieldType": "ZonedDateTime"}, {"fieldName": "specialField1", "fieldType": "String"}, {"fieldName": "specialField2", "fieldType": "String"}, {"fieldName": "specialField3", "fieldType": "String"}, {"fieldName": "matchedField", "fieldType": "String"}, {"fieldName": "paymentType", "fieldType": "String"}, {"fieldName": "paymentDetails", "fieldType": "String"}, {"fieldName": "specialField4", "fieldType": "String"}, {"fieldName": "specialField5", "fieldType": "String"}, {"fieldName": "specialField6", "fieldType": "String"}, {"fieldName": "projectDetails", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "DepositBulkUpload", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "nonReconTransaction", "otherEntityRelationshipName": "depositBulkUpload", "relationshipName": "nonReconTransaction", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "bucketType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "bucketSubType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "depositMode", "relationshipType": "many-to-one"}, {"otherEntityName": "projectBeneficiary", "otherEntityRelationshipName": "depositBulkUpload", "relationshipName": "projectBeneficiary", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "depositBulkUpload", "relationshipName": "project", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "depositBulkUpload", "relationshipName": "unit", "relationshipType": "many-to-one"}, {"otherEntityName": "owner", "otherEntityRelationshipName": "depositBulkUpload", "relationshipName": "owner", "relationshipType": "many-to-one"}], "service": "serviceImpl"}