{"applications": ["Escrow"], "changelogDate": "20220330115224", "dto": "mapstruct", "embedded": false, "entityTableName": "non_recon_transaction", "fields": [{"fieldName": "transactionId", "fieldType": "String"}, {"fieldName": "transactionRefId", "fieldType": "String"}, {"fieldName": "amount", "fieldType": "Double"}, {"fieldName": "totalAmount", "fieldType": "Double"}, {"fieldName": "transactionDate", "fieldType": "ZonedDateTime"}, {"fieldName": "narration", "fieldType": "String"}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "discard", "fieldType": "Boolean"}, {"fieldName": "isAllocated", "fieldType": "Boolean"}, {"fieldName": "transParticular1", "fieldType": "String"}, {"fieldName": "transParticular2", "fieldType": "String"}, {"fieldName": "transRemark1", "fieldType": "String"}, {"fieldName": "transRemark2", "fieldType": "String"}, {"fieldName": "checkNumber", "fieldType": "String"}, {"fieldName": "tasUpdated", "fieldType": "Boolean"}, {"fieldName": "tasUpdate", "fieldType": "Boolean"}, {"fieldName": "unitRefNumber", "fieldType": "String"}, {"fieldName": "valueDateTime", "fieldType": "ZonedDateTime"}, {"fieldName": "postedDateTime", "fieldType": "ZonedDateTime"}, {"fieldName": "normalDateTime", "fieldType": "ZonedDateTime"}, {"fieldName": "branchCode", "fieldType": "String"}, {"fieldName": "postedBranchCode", "fieldType": "String"}, {"fieldName": "currencyCode", "fieldType": "String"}, {"fieldName": "specialField1", "fieldType": "String"}, {"fieldName": "specialField2", "fieldType": "String"}, {"fieldName": "specialField3", "fieldType": "String"}, {"fieldName": "specialField4", "fieldType": "String"}, {"fieldName": "specialField5", "fieldType": "String"}, {"fieldName": "retentionAmount", "fieldType": "Double"}, {"fieldName": "primaryUnitHolderName", "fieldType": "String"}, {"fieldName": "isUnAllocatedCategory", "fieldType": "Boolean"}, {"fieldName": "tasPaymentStatus", "fieldType": "String"}, {"fieldName": "discardedDateTime", "fieldType": "ZonedDateTime"}, {"fieldName": "creditedEscrow", "fieldType": "Boolean"}, {"fieldName": "cbsResponse", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "paymentRefNo", "fieldType": "String"}, {"fieldName": "createdDate", "fieldType": "ZonedDateTime"}, {"fieldName": "specialField6", "fieldType": "String"}, {"fieldName": "specialField7", "fieldType": "String"}, {"fieldName": "retryCount", "fieldType": "Integer"}, {"fieldName": "batchTransactionId", "fieldType": "String"}, {"fieldName": "noqodiBatchTransactionId", "fieldType": "String"}, {"fieldName": "tasInquiryStatus", "fieldType": "String"}, {"fieldName": "payeeDetails1", "fieldType": "String"}, {"fieldName": "payeeDetails2", "fieldType": "String"}, {"fieldName": "payeeDetails3", "fieldType": "String"}, {"fieldName": "payeeDetails4", "fieldType": "String"}, {"fieldName": "manualTransaction", "fieldType": "String"}, {"fieldName": "transferToSubConstruction", "fieldType": "Boolean"}, {"fieldName": "retentionResponse", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "subConstructionResponse", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "t24RetentionRef", "fieldType": "String"}, {"fieldName": "t24SubConstructionRef", "fieldType": "String"}, {"fieldName": "mortgageLandNumber", "fieldType": "String"}, {"fieldName": "tasInflowStatus", "fieldType": "String"}, {"fieldName": "tasInflowResponse", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "tasInquiryResponse", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "tasBatchTransactionId", "fieldType": "String"}, {"fieldName": "processingStatus", "fieldType": "String"}, {"fieldName": "tasTransactionRefNumber", "fieldType": "String"}, {"fieldName": "tasReferenceNumber", "fieldType": "String"}, {"fieldName": "retentionStatus", "fieldType": "String"}, {"fieldName": "subConstructionStatus", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "NonReconTransaction", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "nonReconTransaction", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "nonReconTransaction", "relationshipName": "project", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "nonReconTransaction", "relationshipName": "unit", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "bucketType", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "nonReconTransaction", "relationshipName": "bankAccount", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "depositMode", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "subDepositType", "relationshipType": "many-to-one"}, {"otherEntityName": "depositBulkUpload", "otherEntityRelationshipName": "nonReconTransaction", "relationshipName": "depositBulkUpload", "relationshipType": "one-to-many"}], "service": "serviceImpl"}