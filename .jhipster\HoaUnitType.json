{"applications": ["Escrow"], "changelogDate": "20230129131847", "dto": "mapstruct", "embedded": false, "entityTableName": "hoa_unit_type", "fields": [{"fieldName": "name", "fieldType": "String"}, {"fieldName": "icon", "fieldType": "byte[]", "fieldTypeBlobContent": "image"}, {"fieldName": "isStandalone", "fieldType": "Boolean"}, {"fieldName": "unitTypePrefix", "fieldType": "String"}, {"fieldName": "excelFormula", "fieldType": "String"}, {"fieldName": "jsFormula", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "HoaUnitType", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "hoaUnitType", "otherEntityRelationshipName": "child", "relationshipName": "parent", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "hoaUnitType", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "hoaUnitType", "otherEntityRelationshipName": "parent", "relationshipName": "child", "relationshipType": "one-to-many"}, {"otherEntityName": "hoaUnit", "otherEntityRelationshipName": "hoaUnitType", "relationshipName": "hoaUnit", "relationshipType": "one-to-many"}], "service": "serviceImpl"}