<form *ngIf="bankAccount" name="deleteForm" (ngSubmit)="confirmDelete(bankAccount.id!)">
  <div class="modal-header">
    <h4 class="modal-title" data-cy="bankAccountDeleteDialogHeading">Confirm delete operation</h4>

    <button type="button" class="btn-close" data-dismiss="modal" aria-hidden="true" (click)="cancel()">&times;</button>
  </div>

  <div class="modal-body">
    <ab-alert-error></ab-alert-error>
    <p id="ab-delete-bankAccount-heading">Are you sure you want to delete Bank Account {{ bankAccount.id }}?</p>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
      <fa-icon icon="ban"></fa-icon>&nbsp;<span>Cancel</span>
    </button>

    <button id="ab-confirm-delete-bankAccount" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
      <fa-icon icon="times"></fa-icon>&nbsp;<span>Delete</span>
    </button>
  </div>
</form>
