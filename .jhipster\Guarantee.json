{"applications": ["Escrow"], "changelogDate": "20220330114024", "dto": "mapstruct", "embedded": false, "entityTableName": "guarantee", "fields": [{"fieldName": "guaranteeReferenceNumber", "fieldType": "String"}, {"fieldName": "guaranteeDate", "fieldType": "ZonedDateTime"}, {"fieldName": "name", "fieldType": "String"}, {"fieldName": "openEnded", "fieldType": "Boolean"}, {"fieldName": "guaranteeExpirationDate", "fieldType": "ZonedDateTime"}, {"fieldName": "gaurenteeAmount", "fieldType": "Double"}, {"fieldName": "projectCompletionDate", "fieldType": "ZonedDateTime"}, {"fieldName": "noOfAmendment", "fieldType": "String"}, {"fieldName": "benfContractor", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Guarantee", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "relationshipName": "guaranteeType", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "guarantee", "relationshipName": "project", "relationshipType": "many-to-one"}, {"otherEntityName": "bank", "relationshipName": "issuerBank", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "relationshipName": "accountNumber", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "guaranteeStatus", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "guarantee", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "gaurenteeRecovery", "otherEntityRelationshipName": "guarantee", "relationshipName": "gaurenteeRecovery", "relationshipType": "one-to-many"}, {"otherEntityName": "gau<PERSON><PERSON>Releaser", "otherEntityRelationshipName": "guarantee", "relationshipName": "gau<PERSON><PERSON>Releaser", "relationshipType": "one-to-many"}, {"otherEntityName": "voucherPayment", "otherEntityRelationshipName": "guarantee", "relationshipName": "voucherPayment", "relationshipType": "one-to-many"}], "service": "serviceImpl"}