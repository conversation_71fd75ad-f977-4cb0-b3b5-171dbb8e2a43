{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "child_bank_account_type", "fields": [{"fieldName": "labelId", "fieldType": "String"}, {"fieldName": "typeId", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "ChildBankAccountType", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "masterBankAccountType", "otherEntityRelationshipName": "childBankAccountType", "relationshipName": "masterBankAccountType", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "childBankAccountType", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "childBankAccountType", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}, {"otherEntityName": "bucketRule", "otherEntityRelationshipName": "childBankAccountType", "relationshipName": "bucketRule", "relationshipType": "one-to-many"}], "service": "serviceImpl"}