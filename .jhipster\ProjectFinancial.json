{"applications": ["Escrow"], "changelogDate": "20220330113124", "dto": "mapstruct", "embedded": false, "entityTableName": "project_financial", "fields": [{"fieldName": "estRevenue", "fieldType": "String"}, {"fieldName": "estConstructionCost", "fieldType": "Double"}, {"fieldName": "estProjectMgmtExpense", "fieldType": "Double"}, {"fieldName": "estLandCost", "fieldType": "Double"}, {"fieldName": "estMarketingExpense", "fieldType": "Double"}, {"fieldName": "estimatedDate", "fieldType": "ZonedDateTime"}, {"fieldName": "estExceptionalCapVal", "fieldType": "String"}, {"fieldName": "actualSoldValue", "fieldType": "Double"}, {"fieldName": "actualConstructionCost", "fieldType": "Double"}, {"fieldName": "actualInfraCost", "fieldType": "Double"}, {"fieldName": "actualLandCost", "fieldType": "Double"}, {"fieldName": "actualMarketingExp", "fieldType": "Double"}, {"fieldName": "actualProjectMgmtExpense", "fieldType": "Double"}, {"fieldName": "actualDate", "fieldType": "ZonedDateTime"}, {"fieldName": "actualexceptCapVal", "fieldType": "String"}, {"fieldName": "currentCashReceived", "fieldType": "Double"}, {"fieldName": "curCashRecvdOutEscrow", "fieldType": "Double"}, {"fieldName": "curCashRecvdWithinEscrow", "fieldType": "Double"}, {"fieldName": "curCashRecvdTotal", "fieldType": "Double"}, {"fieldName": "curCashexceptCapVal", "fieldType": "String"}, {"fieldName": "currentLandCost", "fieldType": "Double"}, {"fieldName": "curLandCostOut", "fieldType": "Double"}, {"fieldName": "curLandCost<PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "curLandTotal", "fieldType": "Double"}, {"fieldName": "curLandexceptCapVal", "fieldType": "String"}, {"fieldName": "currentConstructionCost", "fieldType": "Double"}, {"fieldName": "curCons<PERSON>ost<PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "curConsCostOut", "fieldType": "Double"}, {"fieldName": "curConsCostTotal", "fieldType": "Double"}, {"fieldName": "curConsExcepCapVal", "fieldType": "String"}, {"fieldName": "currentMarketingExp", "fieldType": "Double"}, {"fieldName": "currentMktgExpWithin", "fieldType": "Double"}, {"fieldName": "currentMktgExpOut", "fieldType": "Double"}, {"fieldName": "currentMktgExpTotal", "fieldType": "Double"}, {"fieldName": "currentmktgExcepCapVal", "fieldType": "String"}, {"fieldName": "currentProjectMgmtExp", "fieldType": "Double"}, {"fieldName": "curProjMgmtExpWithin", "fieldType": "Double"}, {"fieldName": "curProjMgmtExpOut", "fieldType": "Double"}, {"fieldName": "curProjMgmtExpTotal", "fieldType": "Double"}, {"fieldName": "curProjExcepCapVal", "fieldType": "String"}, {"fieldName": "currentMortgage", "fieldType": "Double"}, {"fieldName": "currentMortgageWithin", "fieldType": "Double"}, {"fieldName": "currentMortgageOut", "fieldType": "Double"}, {"fieldName": "currentMortgageTotal", "fieldType": "Double"}, {"fieldName": "curMortgageExceptCapVal", "fieldType": "String"}, {"fieldName": "currentVatPayment", "fieldType": "Double"}, {"fieldName": "currentVatPaymentWithin", "fieldType": "Double"}, {"fieldName": "currentVatPaymentOut", "fieldType": "Double"}, {"fieldName": "currentVatPaymentTotal", "fieldType": "Double"}, {"fieldName": "curVatExceptCapVal", "fieldType": "String"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "currentOqoodOut", "fieldType": "Double"}, {"fieldName": "currentOqoodTotal", "fieldType": "Double"}, {"fieldName": "curoqoodExceptCapVal", "fieldType": "String"}, {"fieldName": "currentRefund", "fieldType": "Double"}, {"fieldName": "currentRefundWithin", "fieldType": "Double"}, {"fieldName": "currentRefundOut", "fieldType": "Double"}, {"fieldName": "currentRefundTotal", "fieldType": "Double"}, {"fieldName": "curRefundExceptCapVal", "fieldType": "String"}, {"fieldName": "currentBalInRetenAcc", "fieldType": "Double"}, {"fieldName": "curBalInRetenAccWithin", "fieldType": "Double"}, {"fieldName": "curBalInRetenAccOut", "fieldType": "Double"}, {"fieldName": "curBalInRetenAccTotal", "fieldType": "Double"}, {"fieldName": "curBalInRetenExceptCapVal", "fieldType": "String"}, {"fieldName": "currentBalInTrustAcc", "fieldType": "Double"}, {"fieldName": "curBalInTrustAccWithin", "fieldType": "Double"}, {"fieldName": "curBalInTrustAccOut", "fieldType": "Double"}, {"fieldName": "curBalInTrustAccTotal", "fieldType": "Double"}, {"fieldName": "curBalInExceptCapVal", "fieldType": "String"}, {"fieldName": "currentTechnicalFee", "fieldType": "Double"}, {"fieldName": "curT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "curTechnFeeOut", "fieldType": "Double"}, {"fieldName": "curTechnFeeTotal", "fieldType": "Double"}, {"fieldName": "curTechFeeExceptCapVal", "fieldType": "String"}, {"fieldName": "currentUnIdentifiedFund", "fieldType": "Double"}, {"fieldName": "curUnIdeFundWithin", "fieldType": "Double"}, {"fieldName": "curUnIdeFundOut", "fieldType": "Double"}, {"fieldName": "curUnIdeFundTotal", "fieldType": "Double"}, {"fieldName": "curUnIdeExceptCapVal", "fieldType": "String"}, {"fieldName": "currentLoanInstal", "fieldType": "Double"}, {"fieldName": "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "curLoanInstalOut", "fieldType": "Double"}, {"fieldName": "curLoanInstalTotal", "fieldType": "Double"}, {"fieldName": "curLoanExceptCapVal", "fieldType": "String"}, {"fieldName": "currentInfraCost", "fieldType": "Double"}, {"fieldName": "curInfraCostWithin", "fieldType": "Double"}, {"fieldName": "curInfraCostOut", "fieldType": "Double"}, {"fieldName": "curInfraCostTotal", "fieldType": "Double"}, {"fieldName": "curInfraExceptCapVal", "fieldType": "String"}, {"fieldName": "currentOthersCost", "fieldType": "Double"}, {"fieldName": "curO<PERSON>s<PERSON>ost<PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "curOthersCostOut", "fieldType": "Double"}, {"fieldName": "curOthersCostTotal", "fieldType": "Double"}, {"fieldName": "curOthersExceptCapVal", "fieldType": "String"}, {"fieldName": "currentTransferredCost", "fieldType": "Double"}, {"fieldName": "curTransferCostWithin", "fieldType": "Double"}, {"fieldName": "curTransferCostOut", "fieldType": "Double"}, {"fieldName": "curTransferCostTotal", "fieldType": "Double"}, {"fieldName": "curTransferExceptCapVal", "fieldType": "String"}, {"fieldName": "currentForfeitedCost", "fieldType": "Double"}, {"fieldName": "curForfeitCostWithin", "fieldType": "Double"}, {"fieldName": "curForfeitCostOut", "fieldType": "Double"}, {"fieldName": "curForfeitCostTotal", "fieldType": "Double"}, {"fieldName": "curForfeitExceptCapVal", "fieldType": "String"}, {"fieldName": "currentDeveloperEquitycost", "fieldType": "Double"}, {"fieldName": "curDeveEqtycostWithin", "fieldType": "Double"}, {"fieldName": "curDeveEqtycostOut", "fieldType": "Double"}, {"fieldName": "curDeveEqtycostTotal", "fieldType": "Double"}, {"fieldName": "curDeveExceptCapVal", "fieldType": "String"}, {"fieldName": "currentAmantFund", "fieldType": "Double"}, {"fieldName": "curAmntFundWithin", "fieldType": "Double"}, {"fieldName": "curAmntFundOut", "fieldType": "Double"}, {"fieldName": "curAmntFundTotal", "fieldType": "Double"}, {"fieldName": "curAmntExceptCapVal", "fieldType": "String"}, {"fieldName": "currentOtherWithdrawls", "fieldType": "Double"}, {"fieldName": "curO<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "curOtherWithdOut", "fieldType": "Double"}, {"fieldName": "curOtherWithdTotal", "fieldType": "Double"}, {"fieldName": "curOtherExceptCapVal", "fieldType": "String"}, {"fieldName": "currentOqoodOtherFeePay", "fieldType": "Double"}, {"fieldName": "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "curOq<PERSON><PERSON>thFeeOut", "fieldType": "Double"}, {"fieldName": "curOq<PERSON><PERSON>thFeeTotal", "fieldType": "Double"}, {"fieldName": "curOqoodExceptCapVal", "fieldType": "String"}, {"fieldName": "currentVatDeposit", "fieldType": "Double"}, {"fieldName": "curVatDepositWithin", "fieldType": "Double"}, {"fieldName": "curVatDepositOut", "fieldType": "Double"}, {"fieldName": "curVatDepositTotal", "fieldType": "Double"}, {"fieldName": "curVatDepositCapVal", "fieldType": "String"}, {"fieldName": "curBalConstructionTotal", "fieldType": "Double"}, {"fieldName": "curBalConstructionWithin", "fieldType": "Double"}, {"fieldName": "curBalConstructionOut", "fieldType": "Double"}, {"fieldName": "curBalExcepCapVal", "fieldType": "String"}, {"fieldName": "creditInterest", "fieldType": "Double"}, {"fieldName": "paymentForRetentionAcc", "fieldType": "Double"}, {"fieldName": "developerReimburse", "fieldType": "Double"}, {"fieldName": "unitRegFees", "fieldType": "Double"}, {"fieldName": "creditInterestProfit", "fieldType": "Double"}, {"fieldName": "vatCappedCost", "fieldType": "Double"}, {"fieldName": "exceptionalCapVal", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "ProjectFinancial", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "developer", "otherEntityRelationshipName": "projectFinancial", "relationshipName": "developer", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "projectFinancial", "relationshipName": "project", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "projectFinancial", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}