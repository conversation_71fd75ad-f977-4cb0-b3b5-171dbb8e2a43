{"applications": ["Escrow"], "changelogDate": "20220330112624", "dto": "mapstruct", "embedded": false, "entityTableName": "developer_contact", "fields": [{"fieldName": "contactName", "fieldType": "String"}, {"fieldName": "contactTelCode", "fieldType": "String"}, {"fieldName": "contactTelNo", "fieldType": "String"}, {"fieldName": "countryMobCode", "fieldType": "String"}, {"fieldName": "contactMobNo", "fieldType": "String"}, {"fieldName": "contactEmail", "fieldType": "String"}, {"fieldName": "contactAddress", "fieldType": "String"}, {"fieldName": "contactPoBox", "fieldType": "String"}, {"fieldName": "contactFaxNo", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "DeveloperContact", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "developer", "otherEntityRelationshipName": "developerContact", "relationshipName": "developer", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "developerContact", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}