{"applications": ["Escrow"], "changelogDate": "20220330114124", "dto": "mapstruct", "embedded": false, "entityTableName": "gaurentee_recovery", "fields": [{"fieldName": "reductionAmount", "fieldType": "Double"}, {"fieldName": "balanceAmount", "fieldType": "Double"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "GaurenteeRecovery", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "guarantee", "otherEntityRelationshipName": "gaurenteeRecovery", "relationshipName": "guarantee", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "gaurenteeRecovery", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}