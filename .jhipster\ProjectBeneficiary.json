{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "project_beneficiary", "fields": [{"fieldName": "beneficiaryId", "fieldType": "String"}, {"fieldName": "name", "fieldType": "String"}, {"fieldName": "contractAmount", "fieldType": "Double"}, {"fieldName": "actualLandPrice", "fieldType": "Double"}, {"fieldName": "contractorName", "fieldType": "String"}, {"fieldName": "beneficiaryType", "fieldType": "String"}, {"fieldName": "beneficiaryBank", "fieldType": "String"}, {"fieldName": "beneficiarySwift", "fieldType": "String"}, {"fieldName": "beneficiaryRoutingCode", "fieldType": "String"}, {"fieldName": "beneficiary<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "bene<PERSON>nk<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "isActive", "fieldType": "Boolean"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "country", "fieldType": "String"}, {"fieldName": "townLocation", "fieldType": "String"}, {"fieldName": "townName", "fieldType": "String"}, {"fieldName": "districtName", "fieldType": "String"}, {"fieldName": "streetName", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "ProjectBeneficiary", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "relationshipName": "tranferType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "expenseType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "pbVendorSubType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "pbContractorSubType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "pbInfrastructureCategory", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "pbSalesCategory", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "projectBeneficiary", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "projectBeneficiary", "ownerSide": true, "relationshipName": "project", "relationshipType": "many-to-many"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "projectBeneficiary", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}, {"otherEntityName": "voucherPayment", "otherEntityRelationshipName": "projectBeneficiary", "relationshipName": "voucherPayment", "relationshipType": "one-to-many"}, {"otherEntityName": "depositBulkUpload", "otherEntityRelationshipName": "projectBeneficiary", "relationshipName": "depositBulkUpload", "relationshipType": "one-to-many"}], "service": "serviceImpl"}