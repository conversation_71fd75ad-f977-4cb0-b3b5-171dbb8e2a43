{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "bank", "fields": [{"fieldName": "name", "fieldType": "String"}, {"fieldName": "iban", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Bank", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "branch", "otherEntityRelationshipName": "bank", "relationshipName": "branch", "relationshipType": "one-to-many"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "bank", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}, {"otherEntityName": "<PERSON><PERSON><PERSON><PERSON>", "otherEntityRelationshipName": "bank", "relationshipName": "<PERSON><PERSON><PERSON><PERSON>", "relationshipType": "one-to-many"}], "service": "serviceImpl"}