{"applications": ["Escrow"], "changelogDate": "20220330113724", "dto": "mapstruct", "embedded": false, "entityTableName": "owner", "fields": [{"fieldName": "ownerId", "fieldType": "String"}, {"fieldName": "name", "fieldType": "String"}, {"fieldName": "middleName", "fieldType": "String"}, {"fieldName": "lastName", "fieldType": "String"}, {"fieldName": "ownershipPercentage", "fieldType": "Float"}, {"fieldName": "idNo", "fieldType": "String"}, {"fieldName": "contactTel", "fieldType": "String"}, {"fieldName": "mobile", "fieldType": "String"}, {"fieldName": "email", "fieldType": "String"}, {"fieldName": "ownerNumber", "fieldType": "Integer"}, {"fieldName": "isCurrent", "fieldType": "Boolean"}, {"fieldName": "idExpiaryDate", "fieldType": "ZonedDateTime"}, {"fieldName": "localeName", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Owner", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "relationshipName": "documentIdType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "countryOption", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "investerType", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "owner", "relationshipName": "unit", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "owner", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "ownerBankInfo", "otherEntityRelationshipName": "owner", "relationshipName": "ownerBankInfo", "relationshipType": "one-to-many"}, {"otherEntityName": "depositBulkUpload", "otherEntityRelationshipName": "owner", "relationshipName": "depositBulkUpload", "relationshipType": "one-to-many"}], "service": "serviceImpl"}