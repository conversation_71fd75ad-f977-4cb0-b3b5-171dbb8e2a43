{"applications": ["Escrow"], "changelogDate": "20221228113939", "dto": "mapstruct", "embedded": false, "entityTableName": "project_fee_history", "fields": [{"fieldName": "amount", "fieldType": "Double"}, {"fieldName": "totalAmount", "fieldType": "Double"}, {"fieldName": "vatPercentage", "fieldType": "Double"}, {"fieldName": "transactionDate", "fieldType": "ZonedDateTime"}, {"fieldName": "success", "fieldType": "Boolean"}, {"fieldName": "status", "fieldType": "Boolean"}, {"fieldName": "remark", "fieldType": "String"}, {"fieldName": "feeResponse", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "responseStatus", "fieldType": "String"}, {"fieldName": "specialField1", "fieldType": "String"}, {"fieldName": "specialField2", "fieldType": "String"}, {"fieldName": "specialField3", "fieldType": "String"}, {"fieldName": "specialField4", "fieldType": "String"}, {"fieldName": "specialField5", "fieldType": "String"}, {"fieldName": "feeRequestBody", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "ProjectFeeHistory", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "projectFee", "otherEntityRelationshipName": "projectFeeHistory", "relationshipName": "projectFee", "relationshipType": "many-to-one"}, {"otherEntityName": "voucherPayment", "otherEntityRelationshipName": "projectFeeHistory", "relationshipName": "voucherPayment", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "otherEntityRelationshipName": "projectFeeHistory", "relationshipName": "unit", "relationshipType": "many-to-one"}, {"otherEntityName": "project", "otherEntityRelationshipName": "projectFeeHistory", "relationshipName": "project", "relationshipType": "many-to-one"}], "service": "serviceImpl"}