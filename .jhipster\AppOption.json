{"applications": ["Escrow"], "changelogDate": "20220330112024", "dto": "mapstruct", "embedded": false, "entityTableName": "app_option", "fields": [{"fieldName": "optionKey", "fieldType": "String"}, {"fieldName": "optionValue", "fieldType": "String"}, {"fieldName": "labelId", "fieldType": "String"}, {"fieldName": "description", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "AppOption", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "otherEntityRelationshipName": "proj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerSide": true, "relationshipName": "paymentFeeFrequency", "relationshipType": "many-to-many"}, {"otherEntityName": "project", "otherEntityRelationshipName": "bucketType", "ownerSide": false, "relationshipName": "projectOption", "relationshipType": "many-to-many"}, {"otherEntityName": "project", "otherEntityRelationshipName": "blockPayementType", "ownerSide": false, "relationshipName": "projectBlockOption", "relationshipType": "many-to-many"}, {"otherEntityName": "appOption", "otherEntityRelationshipName": "paymentFeeFrequency", "ownerSide": false, "relationshipName": "proj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "relationshipType": "many-to-many"}, {"otherEntityName": "property", "otherEntityRelationshipName": "blockPayementType", "ownerSide": false, "relationshipName": "propertyBlockOption", "relationshipType": "many-to-many"}], "service": "serviceImpl"}