import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { IHoaUnit } from '../hoa-unit.model';

@Component({
  selector: 'ab-hoa-unit-detail',
  templateUrl: './hoa-unit-detail.component.html',
})
export class HoaUnitDetailComponent implements OnInit {
  hoaUnit: IHoaUnit | null = null;

  constructor(protected activatedRoute: ActivatedRoute) {}

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ hoaUnit }) => {
      this.hoaUnit = hoaUnit;
    });
  }

  previousState(): void {
    window.history.back();
  }
}
