{"applications": ["Escrow"], "changelogDate": "20220330113924", "dto": "mapstruct", "embedded": false, "entityTableName": "guarantee_type", "fields": [{"fieldName": "labelId", "fieldType": "String"}, {"fieldName": "typeId", "fieldType": "String"}, {"fieldName": "fetchUrl", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "GuaranteeType", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceImpl"}