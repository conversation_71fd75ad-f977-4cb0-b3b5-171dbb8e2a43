<div class="d-flex justify-content-center">
  <div class="col-8">
    <div *ngIf="bankAccount">
      <h2 data-cy="bankAccountDetailsHeading"><span>Bank Account</span></h2>

      <hr />

      <ab-alert-error></ab-alert-error>

      <ab-alert></ab-alert>

      <dl class="row-md jh-entity-details">
        <dt><span>ID</span></dt>
        <dd>
          <span>{{ bankAccount.id }}</span>
        </dd>
        <dt><span>Bank Account Number</span></dt>
        <dd>
          <span>{{ bankAccount.bankAccountNumber }}</span>
        </dd>
        <dt><span>Bank Account Balance</span></dt>
        <dd>
          <span>{{ bankAccount.bankAccountBalance }}</span>
        </dd>
        <dt><span>Iban No</span></dt>
        <dd>
          <span>{{ bankAccount.ibanNo }}</span>
        </dd>
        <dt><span>Opened Date</span></dt>
        <dd>
          <span>{{ bankAccount.openedDate | formatMediumDatetime }}</span>
        </dd>
        <dt><span>Account Title</span></dt>
        <dd>
          <span>{{ bankAccount.accountTitle }}</span>
        </dd>
        <dt><span>Swiftcode</span></dt>
        <dd>
          <span>{{ bankAccount.swiftcode }}</span>
        </dd>
        <dt><span>Routing Code</span></dt>
        <dd>
          <span>{{ bankAccount.routingCode }}</span>
        </dd>
        <dt><span>Scheme Type</span></dt>
        <dd>
          <span>{{ bankAccount.schemeType }}</span>
        </dd>
        <dt><span>Branch</span></dt>
        <dd>
          <div *ngIf="bankAccount.branch">
            <a [routerLink]="['/branch', bankAccount.branch.id, 'view']">{{ bankAccount.branch.id }}</a>
          </div>
        </dd>
        <dt><span>Bank</span></dt>
        <dd>
          <div *ngIf="bankAccount.bank">
            <a [routerLink]="['/bank', bankAccount.bank.id, 'view']">{{ bankAccount.bank.id }}</a>
          </div>
        </dd>
        <dt><span>Bank Currency</span></dt>
        <dd>
          <div *ngIf="bankAccount.bankCurrency">
            <a [routerLink]="['/app-option', bankAccount.bankCurrency.id, 'view']">{{ bankAccount.bankCurrency.id }}</a>
          </div>
        </dd>
        <dt><span>Master Bank Account Type</span></dt>
        <dd>
          <div *ngIf="bankAccount.masterBankAccountType">
            <a [routerLink]="['/master-bank-account-type', bankAccount.masterBankAccountType.id, 'view']">{{
              bankAccount.masterBankAccountType.id
            }}</a>
          </div>
        </dd>
        <dt><span>Child Bank Account Type</span></dt>
        <dd>
          <div *ngIf="bankAccount.childBankAccountType">
            <a [routerLink]="['/child-bank-account-type', bankAccount.childBankAccountType.id, 'view']">{{
              bankAccount.childBankAccountType.id
            }}</a>
          </div>
        </dd>
        <dt><span>Developer Beneficiary</span></dt>
        <dd>
          <div *ngIf="bankAccount.developerBeneficiary">
            <a [routerLink]="['/developer-beneficiary', bankAccount.developerBeneficiary.id, 'view']">{{
              bankAccount.developerBeneficiary.id
            }}</a>
          </div>
        </dd>
        <dt><span>Project Beneficiary</span></dt>
        <dd>
          <div *ngIf="bankAccount.projectBeneficiary">
            <a [routerLink]="['/project-beneficiary', bankAccount.projectBeneficiary.id, 'view']">{{
              bankAccount.projectBeneficiary.id
            }}</a>
          </div>
        </dd>
        <dt><span>Unit</span></dt>
        <dd>
          <div *ngIf="bankAccount.unit">
            <a [routerLink]="['/unit', bankAccount.unit.id, 'view']">{{ bankAccount.unit.id }}</a>
          </div>
        </dd>
        <dt><span>Project</span></dt>
        <dd>
          <div *ngIf="bankAccount.project">
            <a [routerLink]="['/project', bankAccount.project.id, 'view']">{{ bankAccount.project.id }}</a>
          </div>
        </dd>
        <dt><span>Work Flow States</span></dt>
        <dd>
          <div *ngIf="bankAccount.workFlowStates">
            <a [routerLink]="['/work-flow-states', bankAccount.workFlowStates.id, 'view']">{{ bankAccount.workFlowStates.id }}</a>
          </div>
        </dd>
        <dt><span>Property Beneficiary</span></dt>
        <dd>
          <div *ngIf="bankAccount.propertyBeneficiary">
            <a [routerLink]="['/property-beneficiary', bankAccount.propertyBeneficiary.id, 'view']">{{
              bankAccount.propertyBeneficiary.id
            }}</a>
          </div>
        </dd>
        <dt><span>Hoa Unit</span></dt>
        <dd>
          <div *ngIf="bankAccount.hoaUnit">
            <a [routerLink]="['/hoa-unit', bankAccount.hoaUnit.id, 'view']">{{ bankAccount.hoaUnit.id }}</a>
          </div>
        </dd>
        <dt><span>Property</span></dt>
        <dd>
          <div *ngIf="bankAccount.property">
            <a [routerLink]="['/property', bankAccount.property.id, 'view']">{{ bankAccount.property.id }}</a>
          </div>
        </dd>
      </dl>

      <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
        <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span>Back</span>
      </button>

      <button type="button" [routerLink]="['/bank-account', bankAccount.id, 'edit']" class="btn btn-primary">
        <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span>Edit</span>
      </button>
    </div>
  </div>
</div>
