{"applications": ["Escrow"], "changelogDate": "20220330114224", "dto": "mapstruct", "embedded": false, "entityTableName": "gaurentee_releaser", "fields": [{"fieldName": "requestDate", "fieldType": "ZonedDateTime"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "GaurenteeReleaser", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "guarantee", "otherEntityRelationshipName": "gau<PERSON><PERSON>Releaser", "relationshipName": "guarantee", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "gau<PERSON><PERSON>Releaser", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}