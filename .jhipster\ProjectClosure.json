{"applications": ["Escrow"], "changelogDate": "20220330113224", "dto": "mapstruct", "embedded": false, "entityTableName": "project_closure", "fields": [{"fieldName": "totalIncomeFund", "fieldType": "Double"}, {"fieldName": "totalPayment", "fieldType": "Double"}, {"fieldName": "checkGuranteeDoc", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "ProjectClosure", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "project", "otherEntityRelationshipName": "projectClosure", "relationshipName": "project", "relationshipType": "many-to-one"}, {"otherEntityName": "appDocument", "otherEntityRelationshipName": "projectClosure", "relationshipName": "appDocument", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "projectClosure", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}