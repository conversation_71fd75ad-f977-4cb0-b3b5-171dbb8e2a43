{"applications": ["Escrow"], "changelogDate": "20220330112524", "dto": "mapstruct", "embedded": false, "entityTableName": "developer", "fields": [{"fieldName": "developerId", "fieldType": "String"}, {"fieldName": "cifRera", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "developerRegNo", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "nameLocal", "fieldType": "String"}, {"fieldName": "onboardingDate", "fieldType": "ZonedDateTime"}, {"fieldName": "contactAddress", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "150"}, {"fieldName": "contactTel", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "poBox", "fieldType": "String"}, {"fieldName": "mobile", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "fax", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "email", "fieldType": "String"}, {"fieldName": "licenseNo", "fieldType": "String"}, {"fieldName": "licenseExpDate", "fieldType": "ZonedDateTime"}, {"fieldName": "worldCheckFlag", "fieldType": "Boolean"}, {"fieldName": "worldCheckRemarks", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "migratedData", "fieldType": "Boolean"}, {"fieldName": "remark", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Developer", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "relationshipName": "regulator", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "developerActiveStatus", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "developer", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "developerContact", "otherEntityRelationshipName": "developer", "relationshipName": "developerContact", "relationshipType": "one-to-many"}, {"otherEntityName": "project", "otherEntityRelationshipName": "developer", "relationshipName": "project", "relationshipType": "one-to-many"}, {"otherEntityName": "projectFinancial", "otherEntityRelationshipName": "developer", "relationshipName": "projectFinancial", "relationshipType": "one-to-many"}, {"otherEntityName": "voucherPayment", "otherEntityRelationshipName": "developer", "relationshipName": "voucherPayment", "relationshipType": "one-to-many"}, {"otherEntityName": "developerBeneficiary", "otherEntityRelationshipName": "developer", "ownerSide": false, "relationshipName": "developerBeneficiary", "relationshipType": "many-to-many"}], "service": "serviceImpl"}