import dayjs from 'dayjs/esm';

import { IHoaOwner, NewHoaOwner } from './hoa-owner.model';

export const sampleWithRequiredData: IHoaOwner = {
  id: 87852,
};

export const sampleWithPartialData: IHoaOwner = {
  id: 62352,
  name: 'payment',
  ownerNumber: 58798,
  isCurrent: false,
  idExpiaryDate: dayjs('2023-01-29T09:03'),
  unitMollacId: 'backing hacking',
  floor: 'lime Versatile',
};

export const sampleWithFullData: IHoaOwner = {
  id: 81013,
  ownerId: 'white',
  name: 'Producer markets Maine',
  middleName: 'transmitting Knoll Berkshire',
  lastName: 'Medhurst',
  ownershipPercentage: 75507,
  idNo: 'web-enabled',
  contactTel: 'China',
  mobile: 'Gloves',
  email: '<EMAIL>',
  ownerNumber: 46768,
  isCurrent: true,
  idExpiaryDate: dayjs('2023-01-29T08:12'),
  localeName: 'Identity',
  reservepercentage: 84040,
  unitMollacId: 'Principal',
  noofBedroom: 'transmitter',
  floor: 'Pants systemic',
};

export const sampleWithNewData: NewHoaOwner = {
  id: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
