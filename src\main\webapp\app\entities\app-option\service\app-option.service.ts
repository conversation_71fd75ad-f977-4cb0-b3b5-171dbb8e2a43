import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { IAppOption, NewAppOption } from '../app-option.model';

export type PartialUpdateAppOption = Partial<IAppOption> & Pick<IAppOption, 'id'>;

export type EntityResponseType = HttpResponse<IAppOption>;
export type EntityArrayResponseType = HttpResponse<IAppOption[]>;

@Injectable({ providedIn: 'root' })
export class AppOptionService {
  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/app-options');

  constructor(protected http: HttpClient, protected applicationConfigService: ApplicationConfigService) {}

  create(appOption: NewAppOption): Observable<EntityResponseType> {
    return this.http.post<IAppOption>(this.resourceUrl, appOption, { observe: 'response' });
  }

  update(appOption: IAppOption): Observable<EntityResponseType> {
    return this.http.put<IAppOption>(`${this.resourceUrl}/${this.getAppOptionIdentifier(appOption)}`, appOption, { observe: 'response' });
  }

  partialUpdate(appOption: PartialUpdateAppOption): Observable<EntityResponseType> {
    return this.http.patch<IAppOption>(`${this.resourceUrl}/${this.getAppOptionIdentifier(appOption)}`, appOption, { observe: 'response' });
  }

  find(id: number): Observable<EntityResponseType> {
    return this.http.get<IAppOption>(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<IAppOption[]>(this.resourceUrl, { params: options, observe: 'response' });
  }

  delete(id: number): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  getAppOptionIdentifier(appOption: Pick<IAppOption, 'id'>): number {
    return appOption.id;
  }

  compareAppOption(o1: Pick<IAppOption, 'id'> | null, o2: Pick<IAppOption, 'id'> | null): boolean {
    return o1 && o2 ? this.getAppOptionIdentifier(o1) === this.getAppOptionIdentifier(o2) : o1 === o2;
  }

  addAppOptionToCollectionIfMissing<Type extends Pick<IAppOption, 'id'>>(
    appOptionCollection: Type[],
    ...appOptionsToCheck: (Type | null | undefined)[]
  ): Type[] {
    const appOptions: Type[] = appOptionsToCheck.filter(isPresent);
    if (appOptions.length > 0) {
      const appOptionCollectionIdentifiers = appOptionCollection.map(appOptionItem => this.getAppOptionIdentifier(appOptionItem)!);
      const appOptionsToAdd = appOptions.filter(appOptionItem => {
        const appOptionIdentifier = this.getAppOptionIdentifier(appOptionItem);
        if (appOptionCollectionIdentifiers.includes(appOptionIdentifier)) {
          return false;
        }
        appOptionCollectionIdentifiers.push(appOptionIdentifier);
        return true;
      });
      return [...appOptionsToAdd, ...appOptionCollection];
    }
    return appOptionCollection;
  }
}
