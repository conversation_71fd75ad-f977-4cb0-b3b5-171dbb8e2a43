{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "property_fee", "fields": [{"fieldName": "vatPercentage", "fieldType": "Double"}, {"fieldName": "corporateTax", "fieldType": "Double"}, {"fieldName": "amount", "fieldType": "Double"}, {"fieldName": "debitAccountNo", "fieldType": "String"}, {"fieldName": "feeCollectionDate", "fieldType": "ZonedDateTime"}, {"fieldName": "feeNextRecoveryDate", "fieldType": "ZonedDateTime"}, {"fieldName": "totalAmount", "fieldType": "Double"}, {"fieldName": "feeCollected", "fieldType": "Boolean"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "PropertyFee", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "property", "otherEntityRelationshipName": "propertyFee", "relationshipName": "property", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "propertyFeeCategory", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "paymentFeeFrequency", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "relationshipName": "debitAccount", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "projectFeeCurrency", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "propertyFee", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}], "service": "serviceImpl"}