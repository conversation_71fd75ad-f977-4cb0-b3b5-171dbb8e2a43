import { Component } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import { IHoaUnit } from '../hoa-unit.model';
import { HoaUnitService } from '../service/hoa-unit.service';
import { ITEM_DELETED_EVENT } from 'app/config/navigation.constants';

@Component({
  templateUrl: './hoa-unit-delete-dialog.component.html',
})
export class HoaUnitDeleteDialogComponent {
  hoaUnit?: IHoaUnit;

  constructor(protected hoaUnitService: HoaUnitService, protected activeModal: NgbActiveModal) {}

  cancel(): void {
    this.activeModal.dismiss();
  }

  confirmDelete(id: number): void {
    this.hoaUnitService.delete(id).subscribe(() => {
      this.activeModal.close(ITEM_DELETED_EVENT);
    });
  }
}
