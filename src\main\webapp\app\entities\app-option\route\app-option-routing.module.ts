import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { UserRouteAccessService } from 'app/core/auth/user-route-access.service';
import { AppOptionComponent } from '../list/app-option.component';
import { AppOptionDetailComponent } from '../detail/app-option-detail.component';
import { AppOptionUpdateComponent } from '../update/app-option-update.component';
import { AppOptionRoutingResolveService } from './app-option-routing-resolve.service';
import { ASC } from 'app/config/navigation.constants';

const appOptionRoute: Routes = [
  {
    path: '',
    component: AppOptionComponent,
    data: {
      defaultSort: 'id,' + ASC,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':id/view',
    component: AppOptionDetailComponent,
    resolve: {
      appOption: AppOptionRoutingResolveService,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: 'new',
    component: AppOptionUpdateComponent,
    resolve: {
      appOption: AppOptionRoutingResolveService,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':id/edit',
    component: AppOptionUpdateComponent,
    resolve: {
      appOption: AppOptionRoutingResolveService,
    },
    canActivate: [UserRouteAccessService],
  },
];

@NgModule({
  imports: [RouterModule.forChild(appOptionRoute)],
  exports: [RouterModule],
})
export class AppOptionRoutingModule {}
