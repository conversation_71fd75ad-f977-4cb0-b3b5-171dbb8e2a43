{"applications": ["Escrow"], "changelogDate": "20220330115324", "dto": "mapstruct", "embedded": false, "entityTableName": "bucket_rule_transaction", "fields": [{"fieldName": "parentTransactionId", "fieldType": "String"}, {"fieldName": "reason", "fieldType": "String"}, {"fieldName": "amount", "fieldType": "Double"}, {"fieldName": "transactionId", "fieldType": "String"}, {"fieldName": "transactionDate", "fieldType": "ZonedDateTime"}, {"fieldName": "narration", "fieldType": "String"}, {"fieldName": "description", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "BucketRuleTransaction", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "bucketRuleTransaction", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "relationshipName": "fromA<PERSON>unt", "relationshipType": "many-to-one"}, {"otherEntityName": "bankAccount", "relationshipName": "toAccount", "relationshipType": "many-to-one"}], "service": "serviceImpl"}