{"applications": ["Escrow"], "changelogDate": "20230129131842", "dto": "mapstruct", "embedded": false, "entityTableName": "property", "fields": [{"fieldName": "propertyId", "fieldType": "String"}, {"fieldName": "propertyRera", "fieldType": "String"}, {"fieldName": "propertyAccCif", "fieldType": "String"}, {"fieldName": "name", "fieldType": "String"}, {"fieldName": "location", "fieldType": "String"}, {"fieldName": "companyNumber", "fieldType": "String"}, {"fieldName": "companyName", "fieldType": "String"}, {"fieldName": "propertyGroupId", "fieldType": "String"}, {"fieldName": "projectName", "fieldType": "String"}, {"fieldName": "projectNameLocale", "fieldType": "String"}, {"fieldName": "masterCommunityName", "fieldType": "String"}, {"fieldName": "masterCommunityLocale", "fieldType": "String"}, {"fieldName": "masterDeveloperName", "fieldType": "String"}, {"fieldName": "accStatusDate", "fieldType": "ZonedDateTime"}, {"fieldName": "registrationDate", "fieldType": "ZonedDateTime"}, {"fieldName": "estimatedDate", "fieldType": "ZonedDateTime"}, {"fieldName": "completionDate", "fieldType": "ZonedDateTime"}, {"fieldName": "reservPercentage", "fieldType": "String"}, {"fieldName": "additionalReservePercent", "fieldType": "String"}, {"fieldName": "totalReservePercent", "fieldType": "String"}, {"fieldName": "reserveAccEffStartDate", "fieldType": "ZonedDateTime"}, {"fieldName": "remarks", "fieldType": "byte[]", "fieldTypeBlobContent": "text"}, {"fieldName": "specialApproval", "fieldType": "String"}, {"fieldName": "rmName", "fieldType": "String"}, {"fieldName": "armName", "fieldType": "String"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "accOwner", "fieldType": "String"}, {"fieldName": "accBackupOwner", "fieldType": "String"}, {"fieldName": "emailNotification", "fieldType": "Boolean"}, {"fieldName": "projectManagedBy", "fieldType": "String"}, {"fieldName": "projectMangaedBackup", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Property", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "appOption", "relationshipName": "escrowType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "propertyAccType", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "propertyType", "relationshipType": "many-to-one"}, {"otherEntityName": "propertyMgmtCompany", "relationshipName": "developerMgmtCompany", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "propertyStatus", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "propertyAccountStatus", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "relationshipName": "propertyCurrency", "relationshipType": "many-to-one"}, {"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "property", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "appOption", "otherEntityRelationshipName": "propertyBlockOption", "ownerSide": true, "relationshipName": "blockPayementType", "relationshipType": "many-to-many"}, {"otherEntityName": "propertyFee", "otherEntityRelationshipName": "property", "relationshipName": "propertyFee", "relationshipType": "one-to-many"}, {"otherEntityName": "propertyBeneficiary", "otherEntityRelationshipName": "property", "relationshipName": "propertyBeneficiary", "relationshipType": "one-to-many"}, {"otherEntityName": "propertyFinancial", "otherEntityRelationshipName": "property", "relationshipName": "propertyFinancial", "relationshipType": "one-to-many"}, {"otherEntityName": "hoaUnit", "otherEntityRelationshipName": "property", "relationshipName": "hoaUnit", "relationshipType": "one-to-many"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "property", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}], "service": "serviceImpl"}