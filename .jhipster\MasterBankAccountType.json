{"applications": ["Escrow"], "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "master_bank_account_type", "fields": [{"fieldName": "labelId", "fieldType": "String"}, {"fieldName": "typeId", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "MasterBankAccountType", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "workFlowStates", "otherEntityRelationshipName": "masterBankAccountType", "relationshipName": "workFlowStates", "relationshipType": "many-to-one"}, {"otherEntityName": "childBankAccountType", "otherEntityRelationshipName": "masterBankAccountType", "relationshipName": "childBankAccountType", "relationshipType": "one-to-many"}, {"otherEntityName": "bankAccount", "otherEntityRelationshipName": "masterBankAccountType", "relationshipName": "bankAccount", "relationshipType": "one-to-many"}, {"otherEntityName": "bucketRule", "otherEntityRelationshipName": "masterBankAccountType", "relationshipName": "bucketRule", "relationshipType": "one-to-many"}], "service": "serviceImpl"}