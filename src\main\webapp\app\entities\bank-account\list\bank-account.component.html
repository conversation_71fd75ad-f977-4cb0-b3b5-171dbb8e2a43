<div>
  <h2 id="page-heading" data-cy="BankAccountHeading">
    <span>Bank Accounts</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [spin]="isLoading"></fa-icon>
        <span>Refresh list</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-bank-account"
        [routerLink]="['/bank-account/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span> Create a new Bank Account </span>
      </button>
    </div>
  </h2>

  <ab-alert-error></ab-alert-error>

  <ab-alert></ab-alert>

  <ab-filter [filters]="filters"></ab-filter>

  <div class="alert alert-warning" id="no-result" *ngIf="bankAccounts?.length === 0">
    <span>No Bank Accounts found</span>
  </div>

  <div class="table-responsive table-entities" id="entities" *ngIf="bankAccounts && bankAccounts.length > 0">
    <table class="table table-striped" aria-describedby="page-heading">
      <thead>
        <tr abSort [(predicate)]="predicate" [(ascending)]="ascending" (sortChange)="navigateToWithComponentValues()">
          <th scope="col" abSortBy="id">
            <div class="d-flex">
              <span>ID</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="bankAccountNumber">
            <div class="d-flex">
              <span>Bank Account Number</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="bankAccountBalance">
            <div class="d-flex">
              <span>Bank Account Balance</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="ibanNo">
            <div class="d-flex">
              <span>Iban No</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="openedDate">
            <div class="d-flex">
              <span>Opened Date</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="accountTitle">
            <div class="d-flex">
              <span>Account Title</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="swiftcode">
            <div class="d-flex">
              <span>Swiftcode</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="routingCode">
            <div class="d-flex">
              <span>Routing Code</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="schemeType">
            <div class="d-flex">
              <span>Scheme Type</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="branch.id">
            <div class="d-flex">
              <span>Branch</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="bank.id">
            <div class="d-flex">
              <span>Bank</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="bankCurrency.id">
            <div class="d-flex">
              <span>Bank Currency</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="masterBankAccountType.id">
            <div class="d-flex">
              <span>Master Bank Account Type</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="childBankAccountType.id">
            <div class="d-flex">
              <span>Child Bank Account Type</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="developerBeneficiary.id">
            <div class="d-flex">
              <span>Developer Beneficiary</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="projectBeneficiary.id">
            <div class="d-flex">
              <span>Project Beneficiary</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="unit.id">
            <div class="d-flex">
              <span>Unit</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="project.id">
            <div class="d-flex">
              <span>Project</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="workFlowStates.id">
            <div class="d-flex">
              <span>Work Flow States</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="propertyBeneficiary.id">
            <div class="d-flex">
              <span>Property Beneficiary</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="hoaUnit.id">
            <div class="d-flex">
              <span>Hoa Unit</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col" abSortBy="property.id">
            <div class="d-flex">
              <span>Property</span>
              <fa-icon class="p-1" icon="sort"></fa-icon>
            </div>
          </th>
          <th scope="col"></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let bankAccount of bankAccounts; trackBy: trackId" data-cy="entityTable">
          <td>
            <a [routerLink]="['/bank-account', bankAccount.id, 'view']">{{ bankAccount.id }}</a>
          </td>
          <td>{{ bankAccount.bankAccountNumber }}</td>
          <td>{{ bankAccount.bankAccountBalance }}</td>
          <td>{{ bankAccount.ibanNo }}</td>
          <td>{{ bankAccount.openedDate | formatMediumDatetime }}</td>
          <td>{{ bankAccount.accountTitle }}</td>
          <td>{{ bankAccount.swiftcode }}</td>
          <td>{{ bankAccount.routingCode }}</td>
          <td>{{ bankAccount.schemeType }}</td>
          <td>
            <div *ngIf="bankAccount.branch">
              <a [routerLink]="['/branch', bankAccount.branch.id, 'view']">{{ bankAccount.branch.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.bank">
              <a [routerLink]="['/bank', bankAccount.bank.id, 'view']">{{ bankAccount.bank.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.bankCurrency">
              <a [routerLink]="['/app-option', bankAccount.bankCurrency.id, 'view']">{{ bankAccount.bankCurrency.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.masterBankAccountType">
              <a [routerLink]="['/master-bank-account-type', bankAccount.masterBankAccountType.id, 'view']">{{
                bankAccount.masterBankAccountType.id
              }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.childBankAccountType">
              <a [routerLink]="['/child-bank-account-type', bankAccount.childBankAccountType.id, 'view']">{{
                bankAccount.childBankAccountType.id
              }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.developerBeneficiary">
              <a [routerLink]="['/developer-beneficiary', bankAccount.developerBeneficiary.id, 'view']">{{
                bankAccount.developerBeneficiary.id
              }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.projectBeneficiary">
              <a [routerLink]="['/project-beneficiary', bankAccount.projectBeneficiary.id, 'view']">{{
                bankAccount.projectBeneficiary.id
              }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.unit">
              <a [routerLink]="['/unit', bankAccount.unit.id, 'view']">{{ bankAccount.unit.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.project">
              <a [routerLink]="['/project', bankAccount.project.id, 'view']">{{ bankAccount.project.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.workFlowStates">
              <a [routerLink]="['/work-flow-states', bankAccount.workFlowStates.id, 'view']">{{ bankAccount.workFlowStates.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.propertyBeneficiary">
              <a [routerLink]="['/property-beneficiary', bankAccount.propertyBeneficiary.id, 'view']">{{
                bankAccount.propertyBeneficiary.id
              }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.hoaUnit">
              <a [routerLink]="['/hoa-unit', bankAccount.hoaUnit.id, 'view']">{{ bankAccount.hoaUnit.id }}</a>
            </div>
          </td>
          <td>
            <div *ngIf="bankAccount.property">
              <a [routerLink]="['/property', bankAccount.property.id, 'view']">{{ bankAccount.property.id }}</a>
            </div>
          </td>
          <td class="text-end">
            <div class="btn-group">
              <button
                type="submit"
                [routerLink]="['/owner-bank-info']"
                [queryParams]="{ 'filter[bankAccountId.in]': bankAccount.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Owner Bank Info</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/recon-transaction']"
                [queryParams]="{ 'filter[bankAccountId.in]': bankAccount.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Recon Transaction</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/non-recon-transaction']"
                [queryParams]="{ 'filter[bankAccountId.in]': bankAccount.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Non Recon Transaction</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/voucher-payment']"
                [queryParams]="{ 'filter[bankAccountId.in]': bankAccount.id }"
                class="btn btn-info btn-sm"
                data-cy="filterOtherEntityButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">Show Voucher Payment</span>
              </button>
              <button
                type="submit"
                [routerLink]="['/bank-account', bankAccount.id, 'view']"
                class="btn btn-info btn-sm"
                data-cy="entityDetailsButton"
              >
                <fa-icon icon="eye"></fa-icon>
                <span class="d-none d-md-inline">View</span>
              </button>

              <button
                type="submit"
                [routerLink]="['/bank-account', bankAccount.id, 'edit']"
                class="btn btn-primary btn-sm"
                data-cy="entityEditButton"
              >
                <fa-icon icon="pencil-alt"></fa-icon>
                <span class="d-none d-md-inline">Edit</span>
              </button>

              <button type="submit" (click)="delete(bankAccount)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                <fa-icon icon="times"></fa-icon>
                <span class="d-none d-md-inline">Delete</span>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div *ngIf="bankAccounts && bankAccounts.length > 0">
    <div class="d-flex justify-content-center">
      <ab-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></ab-item-count>
    </div>

    <div class="d-flex justify-content-center">
      <ngb-pagination
        [collectionSize]="totalItems"
        [page]="page"
        [pageSize]="itemsPerPage"
        [maxSize]="5"
        [rotate]="true"
        [boundaryLinks]="true"
        (pageChange)="navigateToPage($event)"
      ></ngb-pagination>
    </div>
  </div>
</div>
